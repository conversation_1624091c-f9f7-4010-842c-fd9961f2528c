#!/usr/bin/env python3
"""
PropertyGuru Analysis Runner
运行所有分析并生成报告的便捷脚本
"""

import os
import sys
import subprocess
from datetime import datetime

def run_console_analysis():
    """运行控制台分析"""
    print("🔍 运行控制台数据分析...")
    print("=" * 60)
    try:
        result = subprocess.run([sys.executable, 'property_analysis.py'], 
                              capture_output=True, text=True, cwd=os.path.dirname(__file__))
        if result.returncode == 0:
            print(result.stdout)
            print("✅ 控制台分析完成")
        else:
            print("❌ 控制台分析失败:")
            print(result.stderr)
    except Exception as e:
        print(f"❌ 运行控制台分析时出错: {e}")
    print()

def run_html_report():
    """生成HTML报告"""
    print("📊 生成HTML可视化报告...")
    print("=" * 60)
    try:
        result = subprocess.run([sys.executable, 'property_report_generator.py'], 
                              capture_output=True, text=True, cwd=os.path.dirname(__file__))
        if result.returncode == 0:
            print(result.stdout)
            print("✅ HTML报告生成完成")
        else:
            print("❌ HTML报告生成失败:")
            print(result.stderr)
    except Exception as e:
        print(f"❌ 生成HTML报告时出错: {e}")
    print()

def open_html_report():
    """打开HTML报告"""
    html_file = os.path.join(os.path.dirname(__file__), 'PropertyGuru_Market_Analysis_Report.html')
    if os.path.exists(html_file):
        print("🌐 打开HTML报告...")
        try:
            if sys.platform.startswith('darwin'):  # macOS
                subprocess.run(['open', html_file])
            elif sys.platform.startswith('win'):   # Windows
                subprocess.run(['start', html_file], shell=True)
            else:  # Linux
                subprocess.run(['xdg-open', html_file])
            print("✅ HTML报告已在浏览器中打开")
        except Exception as e:
            print(f"❌ 打开HTML报告时出错: {e}")
            print(f"请手动打开文件: {html_file}")
    else:
        print("❌ HTML报告文件不存在，请先生成报告")
    print()

def show_report_summary():
    """显示报告摘要"""
    print("📋 报告文件摘要")
    print("=" * 60)
    
    reports_dir = os.path.dirname(__file__)
    files = {
        'PropertyGuru_Market_Analysis_Report.html': '📄 HTML可视化报告',
        'Market_Analysis_Summary.md': '📋 执行摘要文档',
        'property_analysis.py': '🐍 控制台分析脚本',
        'property_report_generator.py': '🐍 HTML报告生成器',
        'README.md': '📖 报告说明文档'
    }
    
    for filename, description in files.items():
        filepath = os.path.join(reports_dir, filename)
        if os.path.exists(filepath):
            size = os.path.getsize(filepath)
            size_str = f"{size:,} bytes" if size < 1024 else f"{size/1024:.1f} KB"
            print(f"  ✅ {description}: {filename} ({size_str})")
        else:
            print(f"  ❌ {description}: {filename} (不存在)")
    print()

def main():
    """主函数"""
    print("🏠 PropertyGuru Singapore Real Estate Analysis")
    print("=" * 60)
    print(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"工作目录: {os.path.dirname(__file__)}")
    print()
    
    while True:
        print("请选择操作:")
        print("1. 运行控制台数据分析")
        print("2. 生成HTML可视化报告")
        print("3. 打开HTML报告")
        print("4. 运行完整分析 (1+2+3)")
        print("5. 查看报告文件摘要")
        print("6. 退出")
        print()
        
        choice = input("请输入选项 (1-6): ").strip()
        
        if choice == '1':
            run_console_analysis()
        elif choice == '2':
            run_html_report()
        elif choice == '3':
            open_html_report()
        elif choice == '4':
            print("🚀 运行完整分析流程...")
            print()
            run_console_analysis()
            run_html_report()
            open_html_report()
            print("🎉 完整分析流程完成!")
            print()
        elif choice == '5':
            show_report_summary()
        elif choice == '6':
            print("👋 再见!")
            break
        else:
            print("❌ 无效选项，请重新选择")
            print()

if __name__ == "__main__":
    main()
