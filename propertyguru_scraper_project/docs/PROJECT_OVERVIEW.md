# PropertyGuru房源爬取系统 - 项目概览

## 📋 项目信息

- **项目名称**: PropertyGuru房源数据爬取系统
- **版本**: 1.0.0
- **开发语言**: Python 3.7+
- **主要框架**: Selenium WebDriver
- **目标网站**: PropertyGuru Singapore

## 🎯 业务目标

### 数据采集范围
- **地理位置**: 9个指定地铁站周边
  - CC13 (Serangoon), CC14 (Lorong Chuan), CC15 (Bishan)
  - CR11 (Ang Mo <PERSON>o), NE12 (Serangoon)
  - NS16 (Ang Mo Kio), NS17 (Bishan), NS18 (Braddell), NS19 (Toa Payoh)
- **价格区间**: S$2,000,000 - S$2,500,000
- **房型要求**: 3-5卧室
- **物业类型**: 非商业住宅

### 预期数据量
- 估计房源数量: ~150个
- 预计页面数量: ~8页
- 爬取时间: 10-15分钟

## 🏗️ 技术架构

### 核心组件
```
PropertyGuruScraper (主爬虫类)
├── setup_driver()      # WebDriver初始化
├── build_url()         # URL构建
├── scrape_page()       # 单页爬取
├── extract_listing_data() # 数据提取
├── scrape_all_pages()  # 批量爬取
├── save_to_csv()       # CSV导出
├── save_to_json()      # JSON导出
└── generate_report()   # 报告生成
```

### 数据流程
```
URL构建 → 页面加载 → 元素定位 → 数据提取 → 数据清洗 → 格式化输出
```

### 反爬策略
1. **用户代理轮换**: 5种不同的User-Agent
2. **随机延迟**: 2-5秒页面间隔
3. **行为模拟**: 页面滚动、等待加载
4. **错误重试**: 最多3次重试机制
5. **超时控制**: 15秒页面加载超时

## 📊 数据模型

### 房源数据结构
```python
{
    "title": str,           # 房源标题
    "price": str,           # 价格 (S$ 格式)
    "address": str,         # 地址
    "bedrooms": str,        # 卧室信息
    "bathrooms": str,       # 浴室信息
    "area": str,            # 面积 (sqft)
    "property_type": str,   # 房产类型
    "built_year": str,      # 建筑年份
    "floor_level": str,     # 楼层
    "facing": str,          # 朝向
    "facilities": str,      # 设施
    "listing_url": str,     # 房源链接
    "listing_date": str,    # 发布日期
    "agent_name": str,      # 代理姓名
    "agent_company": str,   # 代理公司
    "scraped_at": str       # 爬取时间戳
}
```

### 统计数据结构
```python
{
    "total_pages": int,         # 总页数
    "successful_pages": int,    # 成功页数
    "failed_pages": int,        # 失败页数
    "total_listings": int,      # 总房源数
    "start_time": datetime,     # 开始时间
    "end_time": datetime        # 结束时间
}
```

## 🔧 配置管理

### 关键配置项
- **爬虫行为**: 无头模式、重试次数、延迟时间
- **目标参数**: 价格范围、地铁站、房型
- **输出格式**: 文件路径、字段列表、编码
- **日志设置**: 级别、格式、输出位置

### 环境适配
- **macOS**: Chrome路径自动识别
- **Windows**: 支持标准安装路径
- **Linux**: 支持包管理器安装

## 📈 性能指标

### 爬取效率
- **页面处理速度**: 10-15秒/页
- **数据提取速度**: ~20房源/页
- **成功率**: >95%
- **数据完整性**: 
  - 基础信息 (标题、价格、地址): 100%
  - 详细信息 (面积、代理): 90-95%
  - 扩展信息 (楼层、朝向): 30-50%

### 资源消耗
- **内存使用**: ~100-200MB
- **CPU使用**: 中等 (浏览器渲染)
- **网络流量**: ~10-20MB
- **磁盘空间**: <5MB (输出文件)

## 🛡️ 风险控制

### 技术风险
1. **网站结构变化**: CSS选择器失效
   - 缓解: 多重选择器策略
2. **反爬机制升级**: IP封禁、验证码
   - 缓解: 延迟控制、行为模拟
3. **网络不稳定**: 连接超时、数据丢失
   - 缓解: 重试机制、增量保存

### 合规风险
1. **服务条款违反**: 过度爬取
   - 缓解: 频率控制、礼貌爬取
2. **数据使用限制**: 商业用途
   - 缓解: 仅限学习研究

## 🔄 维护计划

### 定期维护
- **选择器更新**: 月度检查网站结构
- **依赖包升级**: 季度更新安全补丁
- **性能优化**: 半年度性能评估

### 功能扩展
- **数据字段**: 增加更多房源属性
- **搜索条件**: 支持更多筛选选项
- **输出格式**: 支持数据库直接导入
- **分析功能**: 集成数据可视化

## 📞 支持信息

### 技术栈
- **Python**: 3.7+ (推荐3.9+)
- **Selenium**: 4.15+ (WebDriver)
- **BeautifulSoup**: 4.12+ (HTML解析)
- **Pandas**: 2.0+ (数据处理)

### 兼容性
- **操作系统**: macOS, Windows, Linux
- **浏览器**: Chrome 90+
- **Python环境**: CPython, 虚拟环境支持

### 文档资源
- **用户手册**: README.md
- **API文档**: 代码注释
- **配置说明**: config.py
- **故障排除**: README.md#故障排除

---

*最后更新: 2024年1月*
