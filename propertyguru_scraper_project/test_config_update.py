#!/usr/bin/env python3
"""
测试配置更新是否生效
"""

import sys
import os
import json

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from config_loader import ConfigLoader
from scraper import PropertyGuruScraper


def test_config_loading():
    """测试配置加载"""
    print("🔍 测试配置文件读取...")
    print("=" * 50)
    
    # 直接读取JSON文件
    config_path = "configs/rent_queenstown_tiong_bahru.json"
    with open(config_path, 'r', encoding='utf-8') as f:
        direct_config = json.load(f)
    
    print(f"📄 直接读取JSON文件:")
    print(f"  minRent: {direct_config.get('minRent')}")
    print(f"  maxRent: {direct_config.get('maxRent')}")
    
    # 通过ConfigLoader读取
    loader = ConfigLoader()
    loaded_config = loader.get_config('rent_queenstown_tiong_bahru')
    
    print(f"\n📋 通过ConfigLoader读取:")
    print(f"  minRent: {loaded_config.get('minRent')}")
    print(f"  maxRent: {loaded_config.get('maxRent')}")
    
    # 检查是否一致
    if direct_config.get('maxRent') == loaded_config.get('maxRent'):
        print("✅ 配置读取一致")
    else:
        print("❌ 配置读取不一致")
    
    return loaded_config


def test_url_generation(config):
    """测试URL生成"""
    print(f"\n🔗 测试URL生成...")
    print("=" * 50)
    
    scraper = PropertyGuruScraper(search_config=config)
    url = scraper.build_url(1)
    
    print(f"生成的URL:")
    print(url)
    
    # 检查URL中的租金参数
    if f"minRent={config.get('minRent')}" in url:
        print(f"✅ minRent参数正确: {config.get('minRent')}")
    else:
        print(f"❌ minRent参数错误")
    
    if f"maxRent={config.get('maxRent')}" in url:
        print(f"✅ maxRent参数正确: {config.get('maxRent')}")
    else:
        print(f"❌ maxRent参数错误")
    
    return url


def test_live_search(url):
    """测试实际搜索效果"""
    print(f"\n🌐 测试实际搜索效果...")
    print("=" * 50)
    
    print("请手动访问以下URL验证搜索结果:")
    print(url)
    print("\n检查要点:")
    print("1. 页面是否正常加载")
    print("2. 搜索结果中的租金是否在设定范围内")
    print("3. 是否有房源显示")


def modify_config_test():
    """修改配置测试"""
    print(f"\n✏️ 修改配置测试...")
    print("=" * 50)
    
    config_path = "configs/rent_queenstown_tiong_bahru.json"
    
    # 读取当前配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    current_max_rent = config.get('maxRent')
    print(f"当前maxRent: {current_max_rent}")
    
    # 临时修改配置
    new_max_rent = "6000"
    config['maxRent'] = new_max_rent
    
    # 保存临时配置
    temp_config_path = "configs/temp_test_config.json"
    with open(temp_config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"临时修改maxRent为: {new_max_rent}")
    
    # 重新加载配置
    loader = ConfigLoader()
    temp_config = loader.get_config('temp_test_config')
    
    if temp_config:
        print(f"重新加载的maxRent: {temp_config.get('maxRent')}")
        
        # 生成新URL
        scraper = PropertyGuruScraper(search_config=temp_config)
        new_url = scraper.build_url(1)
        
        if f"maxRent={new_max_rent}" in new_url:
            print("✅ 配置修改生效")
            print(f"新URL包含: maxRent={new_max_rent}")
        else:
            print("❌ 配置修改未生效")
    
    # 清理临时文件
    if os.path.exists(temp_config_path):
        os.remove(temp_config_path)


def main():
    """主函数"""
    print("🏠 PropertyGuru配置更新测试")
    print("=" * 60)
    
    # 测试配置加载
    config = test_config_loading()
    
    if not config:
        print("❌ 无法加载配置文件")
        return
    
    # 测试URL生成
    url = test_url_generation(config)
    
    # 测试配置修改
    modify_config_test()
    
    # 测试实际搜索效果
    test_live_search(url)
    
    print("\n" + "=" * 60)
    print("💡 如果maxRent修改没有效果，可能的原因:")
    print("1. PropertyGuru网站可能不严格按照URL参数过滤")
    print("2. 网站可能有自己的默认搜索逻辑")
    print("3. 需要检查网站的实际搜索行为")
    print("\n建议:")
    print("- 手动访问生成的URL验证搜索结果")
    print("- 对比不同maxRent值的搜索结果")
    print("- 检查网站是否真的使用了这些参数")


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
