# PropertyGuru房源数据爬取系统

一个专业的PropertyGuru新加坡房产网站数据爬取系统，支持自动化多页面爬取、智能数据提取和详细分析报告。**现已支持销售和租赁房源搜索，提供多种预设配置。**

## 🎯 项目概述

本系统专门用于爬取PropertyGuru网站上符合特定条件的房源信息，提供完整的数据采集、处理和分析功能。

### 🆕 新功能特性
- **双模式支持**: 销售房源 + 租赁房源
- **多区域配置**: Orchard、CBD、Queenstown、Bishan等
- **灵活价格范围**: 支持不同价位段搜索
- **配置文件管理**: JSON格式配置，易于修改和扩展
- **自定义搜索**: 交互式创建个性化搜索条件

### 预设搜索配置
#### 销售房源
- **Bishan/Toa Payoh区域**: S$2M-2.5M, 3-5卧室
- **Orchard区域**: S$1.5M-3M, 2-4卧室
- **东部区域**: S$1M-2M, 3-5卧室

#### 租赁房源
- **Queenstown/Tiong Bahru区域**: 1-4卧室
- **CBD区域**: S$3K-8K/月, 1-3卧室
- **北部区域**: S$2.5K-6K/月, 3-5卧室

### 核心功能
- ✅ 自动多页面爬取（销售+租赁）
- ✅ 智能反爬策略
- ✅ 完整数据提取
- ✅ 数据质量分析
- ✅ 多格式输出
- ✅ 详细报告生成
- ✅ 配置文件管理
- ✅ 自定义搜索条件

## 📋 数据字段

每个房源包含以下详细信息：

| 字段 | 描述 | 示例 |
|------|------|------|
| title | 房源标题 | "Grandeur 8" |
| price | 价格 | "S$ 2,180,000" |
| address | 地址 | "8 Ang Mo Kio Central 3" |
| bedrooms | 卧室信息 | "3 bedroom" |
| bathrooms | 浴室信息 | "2 bathroom" |
| area | 面积 | "1,411 sqft" |
| property_type | 房产类型 | "Condominium" |
| built_year | 建筑年份 | "2015" |
| floor_level | 楼层 | "High floor" |
| facing | 朝向 | "North facing" |
| facilities | 设施 | "Pool, Gym" |
| listing_url | 房源链接 | "https://..." |
| listing_date | 发布日期 | "2024-01-15" |
| agent_name | 代理姓名 | "Linda Lee SG" |
| agent_company | 代理公司 | "PropNex" |
| scraped_at | 爬取时间 | "2024-01-15T10:30:00" |

## 🏗️ 项目结构

```
propertyguru_scraper_project/
├── main.py                 # 主程序入口
├── test_configs.py         # 配置测试脚本
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明文档
├── src/                   # 源代码目录
│   ├── scraper.py         # 核心爬虫类
│   ├── config.py          # 配置文件
│   ├── config_loader.py   # 配置加载器
│   └── utils.py           # 工具函数
├── configs/               # 搜索配置目录
│   ├── README.md          # 配置说明文档
│   ├── sale_bishan_toa_payoh.json    # 销售-Bishan/Toa Payoh
│   ├── sale_orchard.json             # 销售-Orchard区域
│   ├── sale_east.json                # 销售-东部区域
│   ├── rent_queenstown_tiong_bahru.json  # 租赁-Queenstown/Tiong Bahru
│   ├── rent_cbd.json                 # 租赁-CBD区域
│   └── rent_north.json               # 租赁-北部区域
├── output/                # 输出文件目录
├── logs/                  # 日志文件目录
└── docs/                  # 文档目录
```

## 🛠️ 环境要求

### 系统要求
- Python 3.7+
- Chrome浏览器
- 稳定的网络连接

### 依赖包
```bash
requests>=2.31.0          # HTTP请求
beautifulsoup4>=4.12.0     # HTML解析
selenium>=4.15.0           # 浏览器自动化
lxml>=4.9.0               # XML/HTML解析器
pandas>=2.0.0             # 数据处理
python-dateutil>=2.8.2    # 日期处理
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆或下载项目
cd propertyguru_scraper_project

# 安装依赖
pip install -r requirements.txt

# 确保Chrome浏览器已安装
# macOS: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
# Windows: C:\Program Files\Google\Chrome\Application\chrome.exe
# Linux: /usr/bin/google-chrome
```

### 2. 运行程序

```bash
# 运行主程序
python main.py
```

程序会引导你完成以下步骤：

1. **选择搜索配置**：
   - 预设配置（销售/租赁，不同区域）
   - 创建自定义配置
   - 使用默认配置

2. **选择运行模式**：
   - **测试模式**: 爬取前2页，快速验证功能
   - **完整模式**: 爬取所有页面，获取完整数据

### 2.1 配置测试

```bash
# 测试所有配置文件
python test_configs.py
```

这个脚本会验证所有配置文件的正确性和URL构建功能。

### 3. 查看结果

爬取完成后，会在以下目录生成文件：
- `output/` - 数据文件（CSV、JSON）
- `logs/` - 运行日志
- 控制台显示详细统计信息

## 📊 输出文件说明

### CSV文件
- 文件名: `propertyguru_[mode]_YYYYMMDD_HHMMSS.csv`
- 格式: 标准CSV，UTF-8编码
- 用途: Excel分析、数据库导入

### JSON文件
- 文件名: `propertyguru_[mode]_YYYYMMDD_HHMMSS.json`
- 格式: 结构化JSON
- 包含: 爬取统计 + 房源数据
- 用途: 程序处理、API集成

### 报告文件
- 文件名: `scraping_report_YYYYMMDD_HHMMSS.txt`
- 内容: 详细爬取报告
- 包含: 成功率、数据质量、价格分析等

### 日志文件
- 文件名: `logs/scraper.log`
- 内容: 详细运行日志
- 用途: 调试、问题排查

## ⚙️ 配置管理

### 搜索配置文件

所有搜索配置都存储在 `configs/` 目录下的JSON文件中：

```json
{
  "name": "租赁 - Queenstown/Tiong Bahru区域",
  "description": "搜索Queenstown、Redhill、Tiong Bahru地铁站附近的租赁房源",
  "listingType": "rent",
  "isCommercial": "false",
  "mrtStations": ["EW17", "EW18", "EW19"],
  "propertyTypeCode": ["CONDO", "APT", "WALK", "CLUS", "EXCON"],
  "bedrooms": ["1", "2", "3", "4"],
  "minRent": "2000",
  "maxRent": "8000"
}
```

### 可用配置列表

| 配置文件 | 类型 | 区域 | 价格/租金 | 房型 |
|----------|------|------|-----------|------|
| `sale_bishan_toa_payoh.json` | 销售 | Bishan/Toa Payoh | S$2M-2.5M | 3-5卧室 |
| `sale_orchard.json` | 销售 | Orchard | S$1.5M-3M | 2-4卧室 |
| `sale_east.json` | 销售 | 东部区域 | S$1M-2M | 3-5卧室 |
| `rent_queenstown_tiong_bahru.json` | 租赁 | Queenstown/Tiong Bahru | 无限制 | 1-4卧室 |
| `rent_cbd.json` | 租赁 | CBD | S$3K-8K/月 | 1-3卧室 |
| `rent_north.json` | 租赁 | 北部区域 | S$2.5K-6K/月 | 3-5卧室 |

### 创建自定义配置

1. **通过程序创建**：
   ```bash
   python main.py
   # 选择 "创建自定义配置"
   # 按提示输入搜索条件
   ```

2. **手动创建JSON文件**：
   ```bash
   cp configs/rent_queenstown_tiong_bahru.json configs/my_custom_config.json
   # 编辑 my_custom_config.json
   ```

### 爬虫参数 (src/config.py)

```python
SCRAPER_CONFIG = {
    'headless': True,           # 无头模式
    'max_retries': 3,          # 最大重试次数
    'page_load_timeout': 15,   # 页面加载超时
    'min_delay': 2,            # 最小延迟时间
    'max_delay': 5,            # 最大延迟时间
}
```

## 🔧 技术特性

### 反爬策略
- ✅ 随机用户代理轮换
- ✅ 智能延迟控制
- ✅ 真实浏览器模拟
- ✅ 页面滚动行为
- ✅ 错误重试机制

### 数据提取
- ✅ 多种CSS选择器策略
- ✅ 智能文本解析
- ✅ 正则表达式匹配
- ✅ 数据验证和清洗
- ✅ 异常处理

### 性能优化
- ✅ 并发控制
- ✅ 内存管理
- ✅ 增量保存
- ✅ 断点续传支持

## 📈 性能指标

基于实际测试：

| 指标 | 测试模式 | 完整模式 |
|------|----------|----------|
| 爬取速度 | ~20房源/页 | ~20房源/页 |
| 页面耗时 | 10-15秒/页 | 10-15秒/页 |
| 数据完整性 | | |
| - 标题 | 100% | 100% |
| - 价格 | 100% | 100% |
| - 地址 | 100% | 100% |
| - 面积 | ~95% | ~95% |
| - 代理信息 | 100% | 100% |

## 🔍 数据分析功能

### 自动统计
- 数据完整性分析
- 价格分布统计
- 面积范围分析
- 卧室数量分布
- 地铁站分布

### 价格分析
- 平均价格计算
- 价格区间分布
- 每平方英尺价格
- 性价比排序

### 示例输出
```
📊 数据质量统计:
- 有标题: 147/147 (100.0%)
- 有价格: 147/147 (100.0%)
- 有地址: 147/147 (100.0%)
- 有面积: 140/147 (95.2%)

💰 价格分析:
- 平均价格: S$2,240,000
- 最低价格: S$2,000,000
- 最高价格: S$2,500,000
```

## ⚠️ 使用注意事项

### 法律合规
- ✅ 遵守网站robots.txt规则
- ✅ 控制爬取频率
- ✅ 仅用于学习研究目的
- ✅ 尊重网站服务条款

### 技术限制
- 需要稳定网络连接
- Chrome浏览器必须正确安装
- 网站结构变化可能影响爬取
- 反爬机制可能导致暂时阻塞

### 最佳实践
- 建议先运行测试模式
- 避免频繁大量爬取
- 定期更新依赖包
- 监控日志文件

## 🛠️ 故障排除

### 常见问题

1. **Chrome浏览器路径错误**
   ```bash
   # 检查Chrome安装路径
   ls "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
   ```

2. **网络连接超时**
   ```python
   # 增加超时时间 (config.py)
   'page_load_timeout': 30
   ```

3. **数据提取不完整**
   ```bash
   # 检查日志文件
   tail -f logs/scraper.log
   ```

4. **依赖包冲突**
   ```bash
   # 使用虚拟环境
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # venv\Scripts\activate   # Windows
   pip install -r requirements.txt
   ```

### 调试模式

```python
# 启用详细日志
LOGGING_CONFIG = {
    'level': 'DEBUG',  # 改为DEBUG
}

# 关闭无头模式观察浏览器
SCRAPER_CONFIG = {
    'headless': False,  # 改为False
}
```

## 🔄 更新维护

### 选择器更新
如果网站结构变化，更新CSS选择器：
```python
# src/config.py
SELECTORS = {
    'listing_cards': '.new-listing-selector',  # 更新选择器
    # ...
}
```

### 功能扩展
- 添加新的数据字段
- 支持其他搜索条件
- 集成数据库存储
- 添加数据可视化

## 📞 技术支持

### 问题报告
1. 查看日志文件: `logs/scraper.log`
2. 检查网络连接
3. 验证Chrome浏览器版本
4. 确认依赖包版本

### 联系方式
- 项目仓库: [GitHub链接]
- 技术文档: `docs/` 目录
- 问题反馈: Issues页面

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站服务条款。

---

## 🎬 快速演示

### 测试模式演示
```bash
python main.py
# 选择 1 (测试模式)
# 等待2-3分钟完成前2页爬取
# 查看output/目录下的结果文件
```

### 预期结果
- 爬取约40个房源数据
- 生成CSV、JSON和报告文件
- 显示详细统计信息

**免责声明**: 本工具仅用于技术学习和研究目的，使用者需自行承担使用风险，并确保遵守目标网站的服务条款和相关法律法规。
