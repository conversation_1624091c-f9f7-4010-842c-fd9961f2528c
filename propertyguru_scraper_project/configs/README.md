# PropertyGuru 搜索配置文件

这个目录包含了不同的搜索配置文件，用于爬取PropertyGuru网站上的房源数据。每个配置文件定义了特定的搜索条件，包括地理位置、价格范围、房型等。

## 配置文件说明

### 销售房源配置

1. **sale_bishan_toa_payoh.json** - <PERSON><PERSON><PERSON>/Toa Payoh区域销售房源
   - 价格范围: S$2,000,000 - S$2,500,000
   - 地铁站: CC13, CC14, CC15, CR11, NE12, NS16, NS17, NS18, NS19
   - 房型: 3-5卧室
   - 适合: 寻找中高端住宅的买家

2. **sale_orchard.json** - Orchard区域销售房源
   - 价格范围: S$1,500,000 - S$3,000,000
   - 地铁站: NS22, NS23, NS24 (Orchard, Somerset, Dhoby Ghaut)
   - 房型: 2-4卧室
   - 适合: 喜欢都市生活的买家

3. **sale_east.json** - 东部区域销售房源
   - 价格范围: S$1,000,000 - S$2,000,000
   - 地铁站: EW7, EW8, EW9, EW10 (Eunos, Kembangan, Bedok, Tanah Merah)
   - 房型: 3-5卧室
   - 适合: 首次购房者和年轻家庭

### 租赁房源配置

1. **rent_queenstown_tiong_bahru.json** - Queenstown/Tiong Bahru区域租赁房源
   - 地铁站: EW17, EW18, EW19 (Tiong Bahru, Redhill, Queenstown)
   - 房型: 1-4卧室
   - 适合: 寻找Queenstown、Redhill、Tiong Bahru区域租房的用户

2. **rent_cbd.json** - CBD区域租赁房源
   - 租金范围: S$3,000 - S$8,000/月
   - 地铁站: NS25, NS26, EW13, EW14 (City Hall, Raffles Place)
   - 房型: 1-3卧室
   - 适合: 金融从业者和外籍人士

3. **rent_north.json** - 北部区域租赁房源
   - 租金范围: S$2,500 - S$6,000/月
   - 地铁站: NS14, NS15, NS16, NS17 (Khatib, Yio Chu Kang, Ang Mo Kio, Bishan)
   - 房型: 3-5卧室
   - 适合: 家庭居住

## 配置文件格式

每个配置文件都是JSON格式，包含以下字段：

```json
{
  "name": "配置名称",
  "description": "配置描述",
  "listingType": "sale|rent",
  "isCommercial": "false",
  "mrtStations": ["站点代码1", "站点代码2"],
  "_freetextDisplay": "URL编码的显示文本",
  "propertyTypeGroup": "N",
  "propertyTypeCode": ["CONDO", "APT", "WALK", "CLUS", "EXCON"],
  "minPrice": "最低价格（销售）",
  "maxPrice": "最高价格（销售）",
  "minRent": "最低租金（租赁）",
  "maxRent": "最高租金（租赁）",
  "bedrooms": ["卧室数量"],
  "notes": ["说明信息"]
}
```

## 如何使用

1. 运行主程序 `python main.py`
2. 选择配置文件或创建自定义配置
3. 选择测试模式或完整模式
4. 开始爬取数据

## 自定义配置

你可以：
1. 复制现有配置文件并修改参数
2. 使用程序中的"创建自定义配置"功能
3. 手动创建新的JSON配置文件

## 地铁站代码参考

### 常用地铁站代码
- **EW线**: EW7(Eunos), EW8(Kembangan), EW9(Bedok), EW10(Tanah Merah), EW13(City Hall), EW14(Raffles Place), EW17(Tiong Bahru), EW18(Redhill), EW19(Queenstown)
- **NS线**: NS14(Khatib), NS15(Yio Chu Kang), NS16(Ang Mo Kio), NS17(Bishan), NS18(Braddell), NS19(Toa Payoh), NS22(Orchard), NS23(Somerset), NS24(Dhoby Ghaut), NS25(City Hall), NS26(Raffles Place)
- **CC线**: CC13(Serangoon), CC14(Lorong Chuan), CC15(Bishan)
- **CR线**: CR11(Ang Mo Kio)
- **NE线**: NE12(Serangoon)

## 房产类型代码
- **CONDO**: 公寓
- **APT**: 组屋
- **WALK**: 步行式公寓
- **CLUS**: 集群住宅
- **EXCON**: 执行共管公寓

## 注意事项

1. 价格单位为新加坡元（SGD）
2. 租金为月租金
3. 地铁站代码必须准确，否则可能搜索不到结果
4. 配置文件必须是有效的JSON格式
5. 修改配置后需要重新运行程序才能生效
