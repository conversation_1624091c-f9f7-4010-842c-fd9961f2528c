#!/usr/bin/env python3
"""
测试爬虫功能
验证修复后的爬虫是否正常工作
"""

import sys
import os
from datetime import datetime

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from scraper import PropertyGuruScraper
from config_loader import ConfigLoader


def test_single_page():
    """测试单页爬取"""
    print("🧪 测试单页爬取功能...")
    print("=" * 50)
    
    # 使用租赁配置进行测试
    loader = ConfigLoader()
    config = loader.get_config('rent_queenstown_tiong_bahru')
    
    if not config:
        print("❌ 未找到测试配置")
        return False
    
    scraper = PropertyGuruScraper(search_config=config, headless=True, max_retries=2)
    
    try:
        scraper.setup_driver()
        print("✅ WebDriver初始化成功")
        
        # 测试爬取第一页
        page_data = scraper.scrape_page(1)
        
        if page_data:
            print(f"✅ 成功爬取 {len(page_data)} 个房源")
            
            # 显示第一个房源信息
            if page_data:
                first_listing = page_data[0]
                print(f"\n📋 第一个房源信息:")
                print(f"  标题: {first_listing.get('title', 'N/A')}")
                print(f"  价格: {first_listing.get('price', 'N/A')}")
                print(f"  地址: {first_listing.get('address', 'N/A')}")
                print(f"  卧室: {first_listing.get('bedrooms', 'N/A')}")
                print(f"  面积: {first_listing.get('area', 'N/A')}")
            
            return True
        else:
            print("❌ 未能获取到房源数据")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        scraper.cleanup_driver()


def test_url_building():
    """测试URL构建"""
    print("\n🔗 测试URL构建...")
    print("=" * 50)
    
    loader = ConfigLoader()
    
    # 测试租赁配置
    rent_config = loader.get_config('rent_queenstown_tiong_bahru')
    if rent_config:
        scraper = PropertyGuruScraper(search_config=rent_config)
        url = scraper.build_url(1)
        print(f"租赁URL: {url}")
        
        # 验证URL包含必要参数
        required_params = ['listingType=rent', 'mrtStations=EW17', 'mrtStations=EW18', 'mrtStations=EW19']
        for param in required_params:
            if param in url:
                print(f"✅ 包含参数: {param}")
            else:
                print(f"❌ 缺少参数: {param}")
    
    # 测试销售配置
    sale_config = loader.get_config('sale_bishan_toa_payoh')
    if sale_config:
        scraper = PropertyGuruScraper(search_config=sale_config)
        url = scraper.build_url(1)
        print(f"\n销售URL: {url}")
        
        # 验证URL包含必要参数
        required_params = ['listingType=sale', 'minPrice=2000000', 'maxPrice=2500000']
        for param in required_params:
            if param in url:
                print(f"✅ 包含参数: {param}")
            else:
                print(f"❌ 缺少参数: {param}")


def test_driver_stability():
    """测试WebDriver稳定性"""
    print("\n🔧 测试WebDriver稳定性...")
    print("=" * 50)
    
    loader = ConfigLoader()
    config = loader.get_config('rent_queenstown_tiong_bahru')
    
    scraper = PropertyGuruScraper(search_config=config, headless=True, max_retries=1)
    
    try:
        # 测试多次初始化和清理
        for i in range(3):
            print(f"  测试轮次 {i+1}/3")
            scraper.setup_driver()
            print(f"    ✅ 初始化成功")
            
            # 简单访问测试
            url = scraper.build_url(1)
            scraper.driver.get(url)
            print(f"    ✅ 页面访问成功")
            
            scraper.cleanup_driver()
            print(f"    ✅ 清理成功")
        
        print("✅ WebDriver稳定性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ WebDriver稳定性测试失败: {e}")
        return False
    finally:
        scraper.cleanup_driver()


def main():
    """主测试函数"""
    print("🏠 PropertyGuru爬虫修复测试")
    print("=" * 60)
    
    # 测试URL构建
    test_url_building()
    
    # 测试WebDriver稳定性
    stability_ok = test_driver_stability()
    
    if not stability_ok:
        print("\n❌ WebDriver稳定性测试失败，跳过爬取测试")
        return
    
    # 测试单页爬取
    scraping_ok = test_single_page()
    
    print("\n" + "=" * 60)
    if scraping_ok:
        print("✅ 所有测试通过！爬虫修复成功。")
        print("\n💡 建议:")
        print("  - 可以尝试运行完整模式")
        print("  - 如果仍有问题，可以尝试测试模式")
        print("  - 检查网络连接是否稳定")
    else:
        print("❌ 测试失败，需要进一步调试")
        print("\n🔍 调试建议:")
        print("  - 检查Chrome浏览器是否正确安装")
        print("  - 检查网络连接")
        print("  - 查看日志文件: logs/scraper.log")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
