#!/usr/bin/env python3
"""
测试配置系统
验证所有配置文件是否正确加载和URL构建是否正常
"""

import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from config_loader import ConfigLoader
from scraper import PropertyGuruScraper


def test_config_loading():
    """测试配置加载"""
    print("🧪 测试配置加载...")
    print("=" * 50)
    
    loader = ConfigLoader("../configs")
    
    if not loader.configs:
        print("❌ 未找到任何配置文件")
        return False
    
    print(f"✅ 成功加载 {len(loader.configs)} 个配置文件:")
    for config_name in loader.configs.keys():
        info = loader.get_config_info(config_name)
        print(f"  - {info['name']}")
    
    return True


def test_url_building():
    """测试URL构建"""
    print("\n🔗 测试URL构建...")
    print("=" * 50)
    
    loader = ConfigLoader("../configs")

    for config_name, config in loader.configs.items():
        print(f"\n配置: {config.get('name', config_name)}")
        
        try:
            # 创建scraper实例但不初始化driver
            scraper = PropertyGuruScraper(search_config=config)
            
            # 构建URL
            url = scraper.build_url(page=1)
            print(f"✅ URL: {url}")
            
            # 验证URL基本结构
            if config.get('listingType') == 'rent':
                if 'property-for-rent' not in url:
                    print(f"❌ 租赁URL错误")
                    continue
            else:
                if 'property-for-sale' not in url:
                    print(f"❌ 销售URL错误")
                    continue
            
            # 验证必要参数
            required_params = ['listingType', 'page', 'isCommercial']
            for param in required_params:
                if f"{param}=" not in url:
                    print(f"❌ 缺少参数: {param}")
                    continue
            
            print(f"✅ URL验证通过")
            
        except Exception as e:
            print(f"❌ 错误: {e}")


def test_specific_config(config_name):
    """测试特定配置"""
    print(f"\n🎯 测试配置: {config_name}")
    print("=" * 50)
    
    loader = ConfigLoader("../configs")
    config = loader.get_config(config_name)
    
    if not config:
        print(f"❌ 配置 '{config_name}' 不存在")
        return
    
    info = loader.get_config_info(config_name)
    print(f"名称: {info['name']}")
    print(f"类型: {info['listing_type']}")
    print(f"价格: {info['price_range']}")
    print(f"卧室: {', '.join(info['bedrooms']) if info['bedrooms'] else 'Any'}")
    print(f"地铁站: {', '.join(info['mrt_stations'])}")
    
    if info['description']:
        print(f"描述: {info['description']}")
    
    # 构建URL
    scraper = PropertyGuruScraper(search_config=config)
    url = scraper.build_url(page=1)
    print(f"\nURL: {url}")


def main():
    """主函数"""
    print("🏠 PropertyGuru配置系统测试")
    print("=" * 60)
    
    # 测试配置加载
    if not test_config_loading():
        return
    
    # 测试URL构建
    test_url_building()
    
    # 交互式测试
    print("\n" + "=" * 60)
    print("交互式测试 - 输入配置名称进行详细测试")
    print("可用配置:")
    
    loader = ConfigLoader("../configs")
    config_names = list(loader.configs.keys())
    for i, config_name in enumerate(config_names, 1):
        info = loader.get_config_info(config_name)
        print(f"  {i}. {config_name} - {info['name']}")
    
    while True:
        choice = input(f"\n输入配置名称 (或 'quit' 退出): ").strip()
        if choice.lower() == 'quit':
            break
        
        if choice in config_names:
            test_specific_config(choice)
        else:
            print(f"❌ 配置 '{choice}' 不存在")
    
    print("\n✅ 测试完成")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
