#!/usr/bin/env python3
"""
PropertyGuru爬虫系统安装脚本
自动检查环境并安装依赖
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        print(f"当前版本: Python {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True


def check_chrome_browser():
    """检查Chrome浏览器"""
    system = platform.system().lower()
    
    chrome_paths = {
        'darwin': "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
        'windows': "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
        'linux': "/usr/bin/google-chrome"
    }
    
    chrome_path = chrome_paths.get(system)
    if chrome_path and os.path.exists(chrome_path):
        print(f"✅ Chrome浏览器检查通过: {chrome_path}")
        return True
    
    # 尝试在PATH中查找
    try:
        subprocess.run(['google-chrome', '--version'], 
                      capture_output=True, check=True)
        print("✅ Chrome浏览器检查通过: 在PATH中找到")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        pass
    
    try:
        subprocess.run(['chrome', '--version'], 
                      capture_output=True, check=True)
        print("✅ Chrome浏览器检查通过: 在PATH中找到")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        pass
    
    print("❌ 错误: 未找到Chrome浏览器")
    print("请安装Chrome浏览器:")
    print("- macOS: https://www.google.com/chrome/")
    print("- Windows: https://www.google.com/chrome/")
    print("- Linux: sudo apt-get install google-chrome-stable")
    return False


def create_directories():
    """创建必要的目录"""
    directories = ['output', 'logs', 'docs']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ 创建目录: {directory}/")


def install_dependencies():
    """安装Python依赖包"""
    print("📦 安装Python依赖包...")
    
    try:
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ], check=True)
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False


def run_test():
    """运行简单测试"""
    print("🧪 运行环境测试...")
    
    try:
        # 测试导入主要模块
        sys.path.append('src')
        from scraper import PropertyGuruScraper
        print("✅ 模块导入测试通过")
        
        # 测试WebDriver初始化
        scraper = PropertyGuruScraper(headless=True)
        scraper.setup_driver()
        scraper.driver.quit()
        print("✅ WebDriver测试通过")
        
        return True
    except Exception as e:
        print(f"❌ 环境测试失败: {e}")
        return False


def main():
    """主安装流程"""
    print("🏠 PropertyGuru房源爬取系统 - 环境安装")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查Chrome浏览器
    if not check_chrome_browser():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    # 安装依赖
    if not install_dependencies():
        sys.exit(1)
    
    # 运行测试
    if not run_test():
        print("⚠️  环境测试失败，但基本安装已完成")
        print("请手动检查Chrome浏览器和依赖包")
    
    print("\n🎉 安装完成！")
    print("=" * 50)
    print("下一步:")
    print("1. 运行测试: python main.py")
    print("2. 选择测试模式验证功能")
    print("3. 查看README.md了解详细使用方法")
    print("\n📁 项目目录结构:")
    print("- src/     : 源代码")
    print("- output/  : 输出文件")
    print("- logs/    : 日志文件")
    print("- docs/    : 文档")


if __name__ == "__main__":
    main()
