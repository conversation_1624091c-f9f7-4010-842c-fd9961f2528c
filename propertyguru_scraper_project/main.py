#!/usr/bin/env python3
"""
PropertyGuru房源爬取系统 - 主程序
支持完整爬取和测试模式
"""

import sys
import os
import re
from datetime import datetime

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from scraper import PropertyGuruScraper


def analyze_prices(data):
    """分析价格数据"""
    prices = []
    for listing in data:
        if listing['price']:
            # 提取数字价格
            price_match = re.search(r'[\d,]+', listing['price'].replace('S$', '').replace(' ', ''))
            if price_match:
                try:
                    price = int(price_match.group().replace(',', ''))
                    prices.append(price)
                except:
                    pass
    
    if prices:
        return {
            'avg_price': sum(prices) / len(prices),
            'min_price': min(prices),
            'max_price': max(prices),
            'count': len(prices)
        }
    return None


def print_data_quality_stats(data):
    """打印数据质量统计"""
    if not data:
        return
    
    has_title = sum(1 for listing in data if listing['title'])
    has_price = sum(1 for listing in data if listing['price'])
    has_address = sum(1 for listing in data if listing['address'])
    has_area = sum(1 for listing in data if listing['area'])
    has_bedrooms = sum(1 for listing in data if listing['bedrooms'])
    has_agent = sum(1 for listing in data if listing['agent_name'])
    
    print(f"\n📊 数据质量统计:")
    print(f"- 有标题: {has_title}/{len(data)} ({has_title/len(data)*100:.1f}%)")
    print(f"- 有价格: {has_price}/{len(data)} ({has_price/len(data)*100:.1f}%)")
    print(f"- 有地址: {has_address}/{len(data)} ({has_address/len(data)*100:.1f}%)")
    print(f"- 有面积: {has_area}/{len(data)} ({has_area/len(data)*100:.1f}%)")
    print(f"- 有卧室信息: {has_bedrooms}/{len(data)} ({has_bedrooms/len(data)*100:.1f}%)")
    print(f"- 有代理信息: {has_agent}/{len(data)} ({has_agent/len(data)*100:.1f}%)")


def print_sample_listings(data, count=5):
    """打印示例房源"""
    print(f"\n🏠 前{count}个房源预览:")
    print("-" * 80)
    for i, listing in enumerate(data[:count]):
        print(f"{i+1}. {listing['title']}")
        print(f"   💰 价格: {listing['price']}")
        print(f"   📍 地址: {listing['address']}")
        print(f"   🛏️  卧室: {listing['bedrooms'] or '未知'}")
        print(f"   📐 面积: {listing['area'] or '未知'}")
        print(f"   👤 代理: {listing['agent_name'] or '未知'}")
        print(f"   🔗 链接: {listing['listing_url']}")
        print()


def save_detailed_report(scraper, data, timestamp):
    """保存详细报告"""
    report_file = f"output/scraping_report_{timestamp}.txt"
    
    # 生成基础报告
    report = scraper.generate_report()
    
    # 添加额外统计信息
    price_analysis = analyze_prices(data)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
        f.write(f"\n\n详细数据质量统计:\n")
        f.write(f"=" * 40 + "\n")
        
        if data:
            has_title = sum(1 for listing in data if listing['title'])
            has_price = sum(1 for listing in data if listing['price'])
            has_address = sum(1 for listing in data if listing['address'])
            has_area = sum(1 for listing in data if listing['area'])
            has_bedrooms = sum(1 for listing in data if listing['bedrooms'])
            has_agent = sum(1 for listing in data if listing['agent_name'])
            
            f.write(f"- 有标题: {has_title}/{len(data)} ({has_title/len(data)*100:.1f}%)\n")
            f.write(f"- 有价格: {has_price}/{len(data)} ({has_price/len(data)*100:.1f}%)\n")
            f.write(f"- 有地址: {has_address}/{len(data)} ({has_address/len(data)*100:.1f}%)\n")
            f.write(f"- 有面积: {has_area}/{len(data)} ({has_area/len(data)*100:.1f}%)\n")
            f.write(f"- 有卧室信息: {has_bedrooms}/{len(data)} ({has_bedrooms/len(data)*100:.1f}%)\n")
            f.write(f"- 有代理信息: {has_agent}/{len(data)} ({has_agent/len(data)*100:.1f}%)\n")
        
        if price_analysis:
            f.write(f"\n价格分析:\n")
            f.write(f"- 平均价格: S${price_analysis['avg_price']:,.0f}\n")
            f.write(f"- 最低价格: S${price_analysis['min_price']:,.0f}\n")
            f.write(f"- 最高价格: S${price_analysis['max_price']:,.0f}\n")
            f.write(f"- 有效价格数量: {price_analysis['count']}\n")
    
    return report_file


def run_test_mode():
    """运行测试模式 - 只爬取前2页"""
    print("🧪 测试模式 - 爬取前2页")
    print("=" * 50)
    
    scraper = PropertyGuruScraper(headless=True, max_retries=2)
    
    try:
        start_time = datetime.now()
        scraper.scraping_stats['start_time'] = start_time
        
        scraper.setup_driver()
        
        # 爬取前2页
        for page_num in range(1, 3):
            try:
                print(f"正在爬取第 {page_num} 页...")
                page_data = scraper.scrape_page(page_num)
                if page_data:
                    scraper.scraped_data.extend(page_data)
                    scraper.scraping_stats['successful_pages'] += 1
                    scraper.scraping_stats['total_listings'] += len(page_data)
                    print(f"✓ 第{page_num}页: 成功爬取 {len(page_data)} 个房源")
                else:
                    scraper.scraping_stats['failed_pages'] += 1
                    print(f"✗ 第{page_num}页: 爬取失败")
                
                # 页面间延迟
                if page_num < 2:
                    scraper.random_delay(2, 4)
                    
            except Exception as e:
                print(f"✗ 第{page_num}页: 发生错误 - {e}")
                scraper.scraping_stats['failed_pages'] += 1
                continue
        
        scraper.scraping_stats['total_pages'] = 2
        scraper.scraping_stats['end_time'] = datetime.now()
        
        if scraper.driver:
            scraper.driver.quit()
        
        return scraper.scraped_data, scraper
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        if scraper.driver:
            scraper.driver.quit()
        return [], scraper


def run_full_mode():
    """运行完整模式 - 爬取所有页面"""
    print("🚀 完整模式 - 爬取所有页面")
    print("=" * 50)
    
    scraper = PropertyGuruScraper(headless=True, max_retries=3)
    
    try:
        data = scraper.scrape_all_pages()
        return data, scraper
        
    except Exception as e:
        print(f"爬取过程中发生错误: {e}")
        return scraper.scraped_data, scraper


def main():
    """主函数"""
    print("🏠 PropertyGuru房源数据爬取系统")
    print("=" * 60)
    print("目标参数:")
    print("- 地铁站: CC13, CC14, CC15, CR11, NE12, NS16, NS17, NS18, NS19")
    print("- 价格范围: S$2,000,000 - S$2,500,000")
    print("- 房型: 3-5卧室")
    print("- 房产类型: 非商业住宅")
    print("=" * 60)
    
    # 选择运行模式
    print("\n请选择运行模式:")
    print("1. 测试模式 (爬取前2页)")
    print("2. 完整模式 (爬取所有页面)")
    
    while True:
        choice = input("\n请输入选择 (1/2): ").strip()
        if choice in ['1', '2']:
            break
        print("无效选择，请输入 1 或 2")
    
    # 确认开始
    if choice == '2':
        confirm = input("\n完整模式可能需要较长时间，确认开始？(y/n): ").lower().strip()
        if confirm != 'y':
            print("已取消。")
            return
    
    # 开始爬取
    start_time = datetime.now()
    
    if choice == '1':
        data, scraper = run_test_mode()
    else:
        data, scraper = run_full_mode()
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    # 处理结果
    if data:
        print(f"\n🎉 爬取完成！")
        print(f"总耗时: {duration}")
        print(f"总共获取 {len(data)} 个房源")
        
        # 数据质量统计
        print_data_quality_stats(data)
        
        # 价格分析
        price_analysis = analyze_prices(data)
        if price_analysis:
            print(f"\n💰 价格分析:")
            print(f"- 平均价格: S${price_analysis['avg_price']:,.0f}")
            print(f"- 最低价格: S${price_analysis['min_price']:,.0f}")
            print(f"- 最高价格: S${price_analysis['max_price']:,.0f}")
        
        # 显示示例房源
        print_sample_listings(data)
        
        # 保存数据
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        mode_prefix = "test" if choice == '1' else "complete"
        
        csv_file = scraper.save_to_csv(f"output/propertyguru_{mode_prefix}_{timestamp}.csv")
        json_file = scraper.save_to_json(f"output/propertyguru_{mode_prefix}_{timestamp}.json")
        report_file = save_detailed_report(scraper, data, timestamp)
        
        print(f"\n📁 文件已保存:")
        if csv_file:
            print(f"- 📊 CSV数据: {csv_file}")
        if json_file:
            print(f"- 📄 JSON数据: {json_file}")
        print(f"- 📋 详细报告: {report_file}")
        
        print(f"\n✅ 任务完成！共获取 {len(data)} 个房源数据。")
        
        # 提供数据分析建议
        print(f"\n💡 数据分析建议:")
        print(f"- 可以使用Excel或Python pandas分析CSV文件")
        print(f"- 可以按价格、面积、地区等维度进行筛选和排序")
        print(f"- 建议关注价格/面积比例来评估性价比")
        print(f"- 可以分析不同地铁站附近的房价差异")
        
    else:
        print("❌ 未能获取到任何数据，请检查日志文件。")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"❌ 程序发生错误: {e}")
        import traceback
        traceback.print_exc()
