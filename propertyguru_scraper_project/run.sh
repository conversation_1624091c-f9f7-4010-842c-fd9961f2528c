#!/bin/bash
# PropertyGuru房源爬取系统 - 快速运行脚本

echo "🏠 PropertyGuru房源爬取系统"
echo "=========================="
echo "支持销售和租赁房源，多种预设配置可选"
echo ""

# 检查Python
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3"
    exit 1
fi

# 检查是否首次运行
if [ ! -d "output" ] || [ ! -d "logs" ]; then
    echo "📦 首次运行，执行安装..."
    python3 setup.py
    echo ""
fi

# 显示可用选项
echo "可用选项:"
echo "1. 运行主程序 (选择配置并开始爬取)"
echo "2. 测试配置文件"
echo "3. 查看配置说明"
echo ""

read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo "🚀 启动爬虫程序..."
        python3 main.py
        ;;
    2)
        echo "🧪 测试配置文件..."
        python3 test_configs.py
        ;;
    3)
        echo "📋 配置说明:"
        if [ -f "configs/README.md" ]; then
            cat configs/README.md
        else
            echo "配置说明文件不存在"
        fi
        ;;
    *)
        echo "无效选择，启动主程序..."
        python3 main.py
        ;;
esac
