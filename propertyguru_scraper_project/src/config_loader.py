#!/usr/bin/env python3
"""
配置加载器
用于加载和管理不同的搜索配置
"""

import json
import os
import logging
from typing import Dict, List, Optional


class ConfigLoader:
    """配置加载器类"""
    
    def __init__(self, config_dir="configs"):
        self.config_dir = config_dir
        self.configs = {}
        self.load_all_configs()
    
    def load_all_configs(self):
        """加载所有配置文件"""
        if not os.path.exists(self.config_dir):
            logging.warning(f"Config directory {self.config_dir} does not exist")
            return
        
        for filename in os.listdir(self.config_dir):
            if filename.endswith('.json'):
                config_name = filename[:-5]  # 移除.json后缀
                config_path = os.path.join(self.config_dir, filename)
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        self.configs[config_name] = config
                        logging.info(f"Loaded config: {config_name}")
                except Exception as e:
                    logging.error(f"Failed to load config {filename}: {e}")
    
    def get_config(self, config_name: str) -> Optional[Dict]:
        """获取指定配置"""
        return self.configs.get(config_name)
    
    def list_configs(self) -> List[str]:
        """列出所有可用配置"""
        return list(self.configs.keys())
    
    def get_config_info(self, config_name: str) -> Optional[Dict]:
        """获取配置信息（包括描述和说明）"""
        config = self.get_config(config_name)
        if not config:
            return None
        
        return {
            'name': config.get('name', config_name),
            'description': config.get('description', ''),
            'listing_type': config.get('listingType', 'unknown'),
            'mrt_stations': config.get('mrtStations', []),
            'bedrooms': config.get('bedrooms', []),
            'price_range': self._get_price_range(config),
            'notes': config.get('notes', [])
        }
    
    def _get_price_range(self, config: Dict) -> str:
        """获取价格范围描述"""
        if config.get('listingType') == 'sale':
            min_price = config.get('minPrice')
            max_price = config.get('maxPrice')
            if min_price and max_price:
                return f"S${int(min_price):,} - S${int(max_price):,}"
            elif min_price:
                return f"S${int(min_price):,}+"
            elif max_price:
                return f"Up to S${int(max_price):,}"
        elif config.get('listingType') == 'rent':
            min_rent = config.get('minRent')
            max_rent = config.get('maxRent')
            if min_rent and max_rent:
                return f"S${int(min_rent):,} - S${int(max_rent):,}/month"
            elif min_rent:
                return f"S${int(min_rent):,}+/month"
            elif max_rent:
                return f"Up to S${int(max_rent):,}/month"
        
        return "No price limit"
    
    def print_all_configs(self):
        """打印所有配置的摘要信息"""
        if not self.configs:
            print("No configurations found.")
            return
        
        print("Available Configurations:")
        print("=" * 80)
        
        for i, config_name in enumerate(self.configs.keys(), 1):
            info = self.get_config_info(config_name)
            if info:
                print(f"{i}. {info['name']}")
                print(f"   Type: {info['listing_type'].title()}")
                print(f"   Price: {info['price_range']}")
                print(f"   Bedrooms: {', '.join(info['bedrooms']) if info['bedrooms'] else 'Any'}")
                print(f"   MRT Stations: {len(info['mrt_stations'])} stations")
                if info['description']:
                    print(f"   Description: {info['description']}")
                print()
    
    def get_config_by_index(self, index: int) -> Optional[Dict]:
        """通过索引获取配置"""
        config_names = list(self.configs.keys())
        if 1 <= index <= len(config_names):
            config_name = config_names[index - 1]
            return self.get_config(config_name)
        return None
    
    def save_config(self, config_name: str, config: Dict):
        """保存配置到文件"""
        config_path = os.path.join(self.config_dir, f"{config_name}.json")
        
        # 确保配置目录存在
        os.makedirs(self.config_dir, exist_ok=True)
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            # 重新加载配置
            self.configs[config_name] = config
            logging.info(f"Saved config: {config_name}")
            return True
        except Exception as e:
            logging.error(f"Failed to save config {config_name}: {e}")
            return False


def create_custom_config():
    """创建自定义配置的交互式函数"""
    print("Creating Custom Configuration")
    print("=" * 40)
    
    config = {}
    
    # 基本信息
    config['name'] = input("Configuration name: ").strip()
    config['description'] = input("Description (optional): ").strip()
    
    # 列表类型
    while True:
        listing_type = input("Listing type (sale/rent): ").strip().lower()
        if listing_type in ['sale', 'rent']:
            config['listingType'] = listing_type
            break
        print("Please enter 'sale' or 'rent'")
    
    # 基本参数
    config['isCommercial'] = 'false'
    config['propertyTypeGroup'] = 'N'
    config['propertyTypeCode'] = ['CONDO', 'APT', 'WALK', 'CLUS', 'EXCON']
    
    # MRT站点
    print("\nMRT Stations (enter station codes separated by commas, e.g., EW17,EW18,EW19):")
    mrt_input = input("MRT stations: ").strip()
    if mrt_input:
        config['mrtStations'] = [station.strip() for station in mrt_input.split(',')]
    
    # 卧室数量
    print("\nBedrooms (enter numbers separated by commas, e.g., 1,2,3):")
    bedroom_input = input("Bedrooms: ").strip()
    if bedroom_input:
        config['bedrooms'] = [bedroom.strip() for bedroom in bedroom_input.split(',')]
    
    # 价格/租金
    if listing_type == 'sale':
        min_price = input("Minimum price (SGD, optional): ").strip()
        max_price = input("Maximum price (SGD, optional): ").strip()
        if min_price:
            config['minPrice'] = min_price
        if max_price:
            config['maxPrice'] = max_price
    else:
        min_rent = input("Minimum rent (SGD/month, optional): ").strip()
        max_rent = input("Maximum rent (SGD/month, optional): ").strip()
        if min_rent:
            config['minRent'] = min_rent
        if max_rent:
            config['maxRent'] = max_rent
    
    return config


if __name__ == "__main__":
    # 测试配置加载器
    loader = ConfigLoader()
    loader.print_all_configs()
