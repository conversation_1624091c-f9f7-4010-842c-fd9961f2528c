#!/usr/bin/env python3
"""
PropertyGuru房源数据爬取器
主要爬虫类，负责网页爬取和数据提取
"""

import time
import json
import csv
import logging
import random
import re
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class PropertyGuruScraper:
    """PropertyGuru房源爬虫类"""

    def __init__(self, search_config=None, headless=True, max_retries=3):
        self.headless = headless
        self.max_retries = max_retries
        self.driver = None
        self.scraped_data = []
        self.scraping_stats = {
            'total_pages': 0,
            'successful_pages': 0,
            'failed_pages': 0,
            'total_listings': 0,
            'start_time': None,
            'end_time': None
        }

        # 配置日志
        self.setup_logging()

        # 用户代理列表
        self.user_agents = [
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0"
        ]

        # 设置搜索配置
        if search_config:
            self.target_params = search_config.copy()
            # 移除name字段，因为它不是URL参数
            if 'name' in self.target_params:
                self.config_name = self.target_params.pop('name')
            else:
                self.config_name = "Custom Config"
        else:
            # 默认配置（向后兼容）
            self.target_params = {
                'listingType': 'sale',
                'isCommercial': 'false',
                'mrtStations': ['CC13', 'CC14', 'CC15', 'CR11', 'NE12', 'NS16', 'NS17', 'NS18', 'NS19'],
                '_freetextDisplay': 'NS17%2FCC15+Bishan+MRT%2CNS18+Braddell+MRT%2CNS19+Toa+Payoh+MRT%2CNS16%2FCR11+Ang+Mo+Kio+MRT%2CCC14+Lorong+Chuan+MRT%2CNE12%2FCC13+Serangoon+MRT',
                'propertyTypeGroup': 'N',
                'minPrice': '2000000',
                'maxPrice': '2500000',
                'bedrooms': ['3', '4', '5']
            }
            self.config_name = "Default Sale Config"

        # 设置基础URL
        listing_type = self.target_params.get('listingType', 'sale')
        if listing_type == 'rent':
            self.base_url = "https://www.propertyguru.com.sg/property-for-rent"
        else:
            self.base_url = "https://www.propertyguru.com.sg/property-for-sale"

        logging.info(f"Initialized scraper with config: {self.config_name}")
        logging.info(f"Base URL: {self.base_url}")
        logging.info(f"Listing type: {listing_type}")

    def setup_logging(self):
        """设置日志配置"""
        # 确保日志目录存在
        log_dir = 'logs'
        if not os.path.exists(log_dir):
            # 如果当前目录没有logs，尝试上级目录
            parent_log_dir = '../logs'
            if os.path.exists(parent_log_dir):
                log_dir = parent_log_dir
            else:
                # 创建logs目录
                os.makedirs('logs', exist_ok=True)
                log_dir = 'logs'

        log_file = os.path.join(log_dir, 'scraper.log')

        # 清除现有的handlers，避免重复日志
        logger = logging.getLogger()
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # 重新配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, mode='a'),  # 追加模式
                logging.StreamHandler()
            ],
            force=True  # 强制重新配置
        )

        # 记录初始化信息
        logging.info(f"Logging initialized. Log file: {log_file}")

    def setup_driver(self):
        """设置Chrome WebDriver"""
        # 首先清理可能存在的旧进程
        self.cleanup_driver()

        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument('--headless')

        # 增强稳定性的参数
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-features=VizDisplayCompositor')
        chrome_options.add_argument('--remote-debugging-port=9222')

        # 反检测参数
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # 设置页面加载策略
        chrome_options.add_argument('--page-load-strategy=normal')

        # 随机选择用户代理
        user_agent = random.choice(self.user_agents)
        chrome_options.add_argument(f'--user-agent={user_agent}')

        try:
            # 指定Chrome浏览器路径（macOS）
            chrome_options.binary_location = "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"

            # 设置超时时间
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.set_page_load_timeout(30)  # 增加页面加载超时时间
            self.driver.implicitly_wait(10)  # 设置隐式等待

            # 反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
            self.driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})")

            logging.info(f"WebDriver initialized with user agent: {user_agent}")
        except Exception as e:
            logging.error(f"Failed to initialize WebDriver: {e}")
            self.cleanup_driver()
            raise

    def cleanup_driver(self):
        """清理WebDriver资源"""
        if hasattr(self, 'driver') and self.driver:
            try:
                self.driver.quit()
                logging.info("WebDriver cleaned up successfully")
            except Exception as e:
                logging.warning(f"Error cleaning up WebDriver: {e}")
            finally:
                self.driver = None

    def build_url(self, page=1):
        """构建目标URL"""
        params = []
        params.append(f"listingType={self.target_params['listingType']}")
        params.append(f"page={page}")
        params.append(f"isCommercial={self.target_params['isCommercial']}")

        # MRT站点
        if 'mrtStations' in self.target_params:
            for station in self.target_params['mrtStations']:
                params.append(f"mrtStations={station}")

        # 显示文本
        if '_freetextDisplay' in self.target_params:
            params.append(f"_freetextDisplay={self.target_params['_freetextDisplay']}")

        # 房产类型组
        if 'propertyTypeGroup' in self.target_params:
            params.append(f"propertyTypeGroup={self.target_params['propertyTypeGroup']}")

        # 房产类型代码
        if 'propertyTypeCode' in self.target_params:
            for prop_type in self.target_params['propertyTypeCode']:
                params.append(f"propertyTypeCode={prop_type}")

        # 价格范围（销售和租赁都使用minPrice/maxPrice）
        if 'minPrice' in self.target_params:
            params.append(f"minPrice={self.target_params['minPrice']}")
        if 'maxPrice' in self.target_params:
            params.append(f"maxPrice={self.target_params['maxPrice']}")

        # 兼容旧的租金参数名称
        if 'minRent' in self.target_params:
            params.append(f"minPrice={self.target_params['minRent']}")
        if 'maxRent' in self.target_params:
            params.append(f"maxPrice={self.target_params['maxRent']}")

        # 卧室数量
        if 'bedrooms' in self.target_params:
            for bedroom in self.target_params['bedrooms']:
                params.append(f"bedrooms={bedroom}")

        url = f"{self.base_url}?{'&'.join(params)}"
        logging.debug(f"Built URL: {url}")
        return url

    def random_delay(self, min_seconds=2, max_seconds=5):
        """随机延迟"""
        delay = random.uniform(min_seconds, max_seconds)
        logging.info(f"Waiting {delay:.2f} seconds...")
        time.sleep(delay)

    def get_total_pages(self):
        """获取总页数"""
        try:
            # 尝试多种分页选择器
            pagination_selectors = [
                '.pagination a',
                '.pagination-component a',
                '[class*="pagination"] a',
                'a[href*="/property-for-sale/"]',
                'a[href*="/property-for-rent/"]',
                '.page-link',
                '.pagination-link',
                '[data-testid="pagination"] a'
            ]

            page_numbers = []

            for selector in pagination_selectors:
                try:
                    pagination = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if pagination:
                        for link in pagination:
                            text = link.text.strip()
                            if text.isdigit():
                                page_numbers.append(int(text))

                            # 也检查href中的页码
                            href = link.get_attribute('href')
                            if href:
                                page_match = re.search(r'page=(\d+)', href)
                                if page_match:
                                    page_numbers.append(int(page_match.group(1)))

                        # 如果找到了页码，跳出循环
                        if page_numbers:
                            break
                except Exception:
                    continue

            if page_numbers:
                total_pages = max(page_numbers)
                logging.info(f"Detected page numbers: {sorted(set(page_numbers))}, max: {total_pages}")
                return total_pages

            # 如果没有找到分页，尝试从页面内容获取总数
            try:
                # 查找结果总数的文本
                result_selectors = [
                    '.search-results-count',
                    '.total-results',
                    '[class*="result"] [class*="count"]',
                    '.listing-count'
                ]

                for selector in result_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for element in elements:
                            text = element.text
                            # 查找数字
                            numbers = re.findall(r'\d+', text)
                            if numbers:
                                total_listings = int(numbers[-1])  # 取最后一个数字
                                if total_listings > 0:
                                    listings_per_page = 20  # 假设每页20个
                                    estimated_pages = (total_listings + listings_per_page - 1) // listings_per_page
                                    logging.info(f"Estimated {estimated_pages} pages based on {total_listings} total listings")
                                    return min(estimated_pages, 50)  # 限制最大页数
                    except Exception:
                        continue
            except Exception:
                pass

            # 最后尝试：检查当前页面是否有房源，如果有就至少有1页
            try:
                listing_cards = self.driver.find_elements(By.CSS_SELECTOR, '.listing-card-root')
                if listing_cards:
                    logging.info("Found listings on current page, assuming at least 1 page")
                    return 1
            except Exception:
                pass

            logging.warning("Could not determine total pages, defaulting to 1")
            return 1

        except Exception as e:
            logging.warning(f"Could not determine total pages: {e}")
            return 1

    def extract_listing_data(self, listing_element):
        """从房源元素中提取详细信息"""
        data = {
            'title': '',
            'price': '',
            'address': '',
            'bedrooms': '',
            'bathrooms': '',
            'area': '',
            'property_type': '',
            'built_year': '',
            'floor_level': '',
            'facing': '',
            'facilities': '',
            'listing_url': '',
            'listing_date': '',
            'agent_name': '',
            'agent_company': '',
            'scraped_at': datetime.now().isoformat()
        }

        try:
            # 标题
            title_elem = listing_element.find_elements(By.CSS_SELECTOR, '.listing-title')
            if title_elem:
                data['title'] = title_elem[0].text.strip()

            # 链接
            link_elem = listing_element.find_elements(By.CSS_SELECTOR, '.listing-card-link')
            if link_elem and link_elem[0].get_attribute('href'):
                data['listing_url'] = link_elem[0].get_attribute('href')

            # 价格
            price_elem = listing_element.find_elements(By.CSS_SELECTOR, '.listing-price')
            if price_elem:
                data['price'] = price_elem[0].text.strip()

            # 地址
            address_elem = listing_element.find_elements(By.CSS_SELECTOR, '.listing-location')
            if address_elem:
                data['address'] = address_elem[0].text.strip()

            # 代理信息
            agent_elem = listing_element.find_elements(By.CSS_SELECTOR, '.agent-name')
            if agent_elem:
                data['agent_name'] = agent_elem[0].text.strip()

            # 从整体文本中提取房间和面积信息
            all_text = listing_element.text
            lines = all_text.split('\n')

            for line in lines:
                line_lower = line.strip().lower()
                # 查找卧室信息
                if 'bed' in line_lower and ('room' in line_lower or 'br' in line_lower):
                    data['bedrooms'] = line.strip()
                # 查找浴室信息
                elif 'bath' in line_lower and ('room' in line_lower or 'br' in line_lower):
                    data['bathrooms'] = line.strip()
                # 查找面积信息
                elif any(unit in line_lower for unit in ['sqft', 'sq ft', 'sqm', 'sq m']):
                    # 使用正则表达式提取面积
                    area_match = re.search(r'(\d+[,\d]*)\s*(sqft|sq ft|sqm|sq m)', line_lower)
                    if area_match:
                        data['area'] = f"{area_match.group(1)} {area_match.group(2)}"
                # 查找楼层信息
                elif 'floor' in line_lower or 'level' in line_lower:
                    if any(word in line_lower for word in ['high', 'low', 'mid', 'top']):
                        data['floor_level'] = line.strip()

            # 尝试从描述中提取卧室数量
            if not data['bedrooms']:
                bed_match = re.search(r'(\d+)\s*bed', all_text.lower())
                if bed_match:
                    data['bedrooms'] = f"{bed_match.group(1)} bedrooms"

            # 尝试从描述中提取浴室数量
            if not data['bathrooms']:
                bath_match = re.search(r'(\d+)\s*bath', all_text.lower())
                if bath_match:
                    data['bathrooms'] = f"{bath_match.group(1)} bathrooms"

        except Exception as e:
            logging.error(f"Error extracting listing data: {e}")

        return data

    def scrape_page(self, page_num):
        """爬取单个页面"""
        url = self.build_url(page_num)
        logging.info(f"Scraping page {page_num}: {url}")

        for attempt in range(self.max_retries):
            try:
                # 检查driver是否还有效
                if not self.driver:
                    logging.warning("Driver is None, reinitializing...")
                    self.setup_driver()

                # 尝试访问页面
                self.driver.get(url)

                # 等待页面加载，增加超时时间
                WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, '.listing-card-root'))
                )

                # 模拟人类行为 - 滚动页面
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
                time.sleep(2)  # 增加等待时间
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)

                # 查找所有房源卡片
                listing_cards = self.driver.find_elements(By.CSS_SELECTOR, '.listing-card-root')

                if not listing_cards:
                    logging.warning(f"No listing cards found on page {page_num}")
                    # 尝试其他选择器
                    alternative_selectors = [
                        '.listing-card',
                        '[data-testid="listing-card"]',
                        '.property-card',
                        '.listing-item'
                    ]
                    for selector in alternative_selectors:
                        listing_cards = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if listing_cards:
                            logging.info(f"Found {len(listing_cards)} cards with selector: {selector}")
                            break

                    if not listing_cards:
                        return []

                page_data = []
                for i, card in enumerate(listing_cards):
                    try:
                        listing_data = self.extract_listing_data(card)
                        if listing_data['title']:  # 只保存有标题的房源
                            page_data.append(listing_data)
                            logging.debug(f"Extracted listing {i+1}: {listing_data['title']}")
                    except Exception as e:
                        logging.error(f"Error extracting listing {i+1} on page {page_num}: {e}")
                        continue

                logging.info(f"Successfully scraped {len(page_data)} listings from page {page_num}")
                return page_data

            except TimeoutException:
                logging.warning(f"Timeout on page {page_num}, attempt {attempt + 1}")
                if attempt < self.max_retries - 1:
                    # 重新初始化driver
                    try:
                        self.cleanup_driver()
                        self.setup_driver()
                    except Exception as setup_error:
                        logging.error(f"Failed to reinitialize driver: {setup_error}")
                    self.random_delay(10, 15)  # 增加延迟时间
                    continue
                else:
                    logging.error(f"Failed to load page {page_num} after {self.max_retries} attempts")
                    return []
            except Exception as e:
                logging.error(f"Error scraping page {page_num}, attempt {attempt + 1}: {e}")
                if attempt < self.max_retries - 1:
                    # 如果是连接错误，重新初始化driver
                    if "Connection" in str(e) or "WebDriver" in str(e):
                        try:
                            self.cleanup_driver()
                            self.setup_driver()
                        except Exception as setup_error:
                            logging.error(f"Failed to reinitialize driver: {setup_error}")
                    self.random_delay(10, 15)
                    continue
                else:
                    return []

        return []

    def scrape_all_pages(self):
        """爬取所有页面"""
        self.scraping_stats['start_time'] = datetime.now()
        logging.info("Starting PropertyGuru scraping...")
        logging.info(f"Using configuration: {self.config_name}")

        try:
            # 初始化driver
            max_init_retries = 3
            for init_attempt in range(max_init_retries):
                try:
                    self.setup_driver()
                    break
                except Exception as e:
                    logging.error(f"Failed to initialize driver, attempt {init_attempt + 1}: {e}")
                    if init_attempt < max_init_retries - 1:
                        time.sleep(5)
                        continue
                    else:
                        raise Exception("Failed to initialize WebDriver after multiple attempts")

            # 首先访问第一页获取总页数
            first_page_url = self.build_url(1)
            logging.info(f"Accessing first page: {first_page_url}")

            max_page_retries = 3
            for page_attempt in range(max_page_retries):
                try:
                    self.driver.get(first_page_url)

                    # 等待页面加载
                    WebDriverWait(self.driver, 20).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, '.listing-card-root'))
                    )
                    break
                except Exception as e:
                    logging.warning(f"Failed to load first page, attempt {page_attempt + 1}: {e}")
                    if page_attempt < max_page_retries - 1:
                        time.sleep(10)
                        # 重新初始化driver
                        self.cleanup_driver()
                        self.setup_driver()
                        continue
                    else:
                        raise Exception("Failed to load first page after multiple attempts")

            total_pages = self.get_total_pages()
            self.scraping_stats['total_pages'] = total_pages
            logging.info(f"Found {total_pages} pages to scrape")

            # 爬取所有页面
            consecutive_failures = 0
            max_consecutive_failures = 3
            max_total_time = 3600  # 最大运行时间1小时
            start_time = time.time()

            for page_num in range(1, total_pages + 1):
                # 检查是否超时
                if time.time() - start_time > max_total_time:
                    logging.warning(f"Scraping timeout after {max_total_time} seconds, stopping")
                    break
                try:
                    logging.info(f"Processing page {page_num}/{total_pages}")
                    page_data = self.scrape_page(page_num)

                    if page_data:
                        self.scraped_data.extend(page_data)
                        self.scraping_stats['successful_pages'] += 1
                        self.scraping_stats['total_listings'] += len(page_data)
                        consecutive_failures = 0  # 重置失败计数
                        logging.info(f"✅ Page {page_num}: {len(page_data)} listings")
                    else:
                        self.scraping_stats['failed_pages'] += 1
                        consecutive_failures += 1
                        logging.warning(f"❌ Page {page_num}: No data retrieved")

                        # 如果连续失败太多次，停止爬取
                        if consecutive_failures >= max_consecutive_failures:
                            logging.error(f"Too many consecutive failures ({consecutive_failures}), stopping scraping")
                            break

                    # 页面间随机延迟
                    if page_num < total_pages:
                        delay_time = random.uniform(3, 8)  # 增加延迟时间
                        logging.info(f"Waiting {delay_time:.1f} seconds before next page...")
                        time.sleep(delay_time)

                except KeyboardInterrupt:
                    logging.info("User interrupted scraping")
                    break
                except Exception as e:
                    logging.error(f"Failed to scrape page {page_num}: {e}")
                    self.scraping_stats['failed_pages'] += 1
                    consecutive_failures += 1

                    if consecutive_failures >= max_consecutive_failures:
                        logging.error(f"Too many consecutive failures, stopping scraping")
                        break

                    # 等待一段时间后继续
                    time.sleep(10)
                    continue

        except KeyboardInterrupt:
            logging.info("User interrupted scraping")
        except Exception as e:
            logging.error(f"Critical error during scraping: {e}")
            import traceback
            logging.error(traceback.format_exc())
        finally:
            # 确保清理资源
            self.cleanup_driver()
            self.scraping_stats['end_time'] = datetime.now()

        logging.info(f"Scraping completed. Total listings: {len(self.scraped_data)}")
        logging.info(f"Success rate: {self.scraping_stats['successful_pages']}/{self.scraping_stats['total_pages']} pages")
        return self.scraped_data

    def save_to_csv(self, filename=None):
        """保存数据到CSV文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"output/propertyguru_listings_{timestamp}.csv"

        if not self.scraped_data:
            logging.warning("No data to save")
            return None

        fieldnames = [
            'title', 'price', 'address', 'bedrooms', 'bathrooms', 'area',
            'property_type', 'built_year', 'floor_level', 'facing', 'facilities',
            'listing_url', 'listing_date', 'agent_name', 'agent_company', 'scraped_at'
        ]

        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for listing in self.scraped_data:
                    writer.writerow(listing)

            logging.info(f"Data saved to {filename}")
            return filename
        except Exception as e:
            logging.error(f"Error saving CSV file: {e}")
            return None

    def save_to_json(self, filename=None):
        """保存数据到JSON文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"output/propertyguru_listings_{timestamp}.json"

        if not self.scraped_data:
            logging.warning("No data to save")
            return None

        try:
            # 转换datetime对象为字符串
            stats_copy = self.scraping_stats.copy()
            if stats_copy.get('start_time'):
                stats_copy['start_time'] = stats_copy['start_time'].isoformat()
            if stats_copy.get('end_time'):
                stats_copy['end_time'] = stats_copy['end_time'].isoformat()

            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump({
                    'scraping_stats': stats_copy,
                    'listings': self.scraped_data,
                    'total_count': len(self.scraped_data)
                }, jsonfile, indent=2, ensure_ascii=False)

            logging.info(f"Data saved to {filename}")
            return filename
        except Exception as e:
            logging.error(f"Error saving JSON file: {e}")
            return None

    def generate_report(self):
        """生成爬取报告"""
        if not self.scraping_stats['start_time'] or not self.scraping_stats['end_time']:
            return "No scraping session completed"

        duration = self.scraping_stats['end_time'] - self.scraping_stats['start_time']
        success_rate = (self.scraping_stats['successful_pages'] /
                       max(self.scraping_stats['total_pages'], 1)) * 100

        report = f"""
PropertyGuru 房源爬取报告
==========================================
开始时间: {self.scraping_stats['start_time'].strftime('%Y-%m-%d %H:%M:%S')}
结束时间: {self.scraping_stats['end_time'].strftime('%Y-%m-%d %H:%M:%S')}
总耗时: {duration}

页面统计:
- 总页数: {self.scraping_stats['total_pages']}
- 成功页数: {self.scraping_stats['successful_pages']}
- 失败页数: {self.scraping_stats['failed_pages']}
- 成功率: {success_rate:.1f}%

房源统计:
- 总房源数: {len(self.scraped_data)}
- 平均每页房源数: {len(self.scraped_data) / max(self.scraping_stats['successful_pages'], 1):.1f}

数据质量:
- 有标题的房源: {sum(1 for listing in self.scraped_data if listing['title'])}
- 有价格的房源: {sum(1 for listing in self.scraped_data if listing['price'])}
- 有地址的房源: {sum(1 for listing in self.scraped_data if listing['address'])}
- 有链接的房源: {sum(1 for listing in self.scraped_data if listing['listing_url'])}
==========================================
        """

        return report.strip()
