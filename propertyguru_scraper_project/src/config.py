#!/usr/bin/env python3
"""
PropertyGuru爬虫配置文件
包含所有可配置的参数和设置
"""

# 目标网站配置
BASE_URLS = {
    'sale': "https://www.propertyguru.com.sg/property-for-sale",
    'rent': "https://www.propertyguru.com.sg/property-for-rent"
}

# 预定义搜索配置
SEARCH_CONFIGS = {
    # 原有的销售配置 - Bishan/Toa Payoh区域
    'sale_bishan_toa_payoh': {
        'name': '销售 - <PERSON>ishan/Toa Payoh区域 (S$2M-2.5M)',
        'listingType': 'sale',
        'isCommercial': 'false',
        'mrtStations': ['CC13', 'CC14', 'CC15', 'CR11', 'NE12', 'NS16', 'NS17', 'NS18', 'NS19'],
        '_freetextDisplay': 'NS17%2FCC15+<PERSON><PERSON><PERSON>+MRT%2CNS18+<PERSON><PERSON>+MRT%2CNS19+To<PERSON>+<PERSON>oh+MRT%2CNS16%2FCR11+Ang+Mo+Kio+MRT%2CCC14+<PERSON>rong+<PERSON><PERSON>+MRT%2CNE12%2FCC13+Serangoon+MRT',
        'propertyTypeGroup': 'N',
        'propertyTypeCode': ['CONDO', 'APT', 'WALK', 'CLUS', 'EXCON'],
        'minPrice': '2000000',
        'maxPrice': '2500000',
        'bedrooms': ['3', '4', '5']
    },

    # 新的租赁配置 - Queenstown/Tiong Bahru区域
    'rent_queenstown_tiong_bahru': {
        'name': '租赁 - Queenstown/Tiong Bahru区域',
        'listingType': 'rent',
        'isCommercial': 'false',
        'mrtStations': ['EW17', 'EW18', 'EW19'],
        '_freetextDisplay': 'EW19+Queenstown+MRT%2CEW18+Redhill+MRT%2CEW17+Tiong+Bahru+MRT',
        'propertyTypeGroup': 'N',
        'propertyTypeCode': ['CONDO', 'APT', 'WALK', 'CLUS', 'EXCON']
    },

    # 销售配置 - Orchard区域
    'sale_orchard': {
        'name': '销售 - Orchard区域 (S$1.5M-3M)',
        'listingType': 'sale',
        'isCommercial': 'false',
        'mrtStations': ['NS22', 'NS23', 'NS24'],
        '_freetextDisplay': 'NS22+Orchard+MRT%2CNS23+Somerset+MRT%2CNS24+Dhoby+Ghaut+MRT',
        'propertyTypeGroup': 'N',
        'propertyTypeCode': ['CONDO', 'APT', 'WALK', 'CLUS', 'EXCON'],
        'minPrice': '1500000',
        'maxPrice': '3000000',
        'bedrooms': ['2', '3', '4']
    },

    # 租赁配置 - CBD区域
    'rent_cbd': {
        'name': '租赁 - CBD区域',
        'listingType': 'rent',
        'isCommercial': 'false',
        'mrtStations': ['NS25', 'NS26', 'EW13', 'EW14'],
        '_freetextDisplay': 'NS25+City+Hall+MRT%2CNS26+Raffles+Place+MRT%2CEW13+City+Hall+MRT%2CEW14+Raffles+Place+MRT',
        'propertyTypeGroup': 'N',
        'propertyTypeCode': ['CONDO', 'APT', 'WALK', 'CLUS', 'EXCON'],
        'minRent': '3000',
        'maxRent': '8000',
        'bedrooms': ['1', '2', '3']
    },

    # 销售配置 - 东部区域
    'sale_east': {
        'name': '销售 - 东部区域 (S$1M-2M)',
        'listingType': 'sale',
        'isCommercial': 'false',
        'mrtStations': ['EW7', 'EW8', 'EW9', 'EW10'],
        '_freetextDisplay': 'EW7+Eunos+MRT%2CEW8+Kembangan+MRT%2CEW9+Bedok+MRT%2CEW10+Tanah+Merah+MRT',
        'propertyTypeGroup': 'N',
        'propertyTypeCode': ['CONDO', 'APT', 'WALK', 'CLUS', 'EXCON'],
        'minPrice': '1000000',
        'maxPrice': '2000000',
        'bedrooms': ['3', '4', '5']
    },

    # 租赁配置 - 北部区域
    'rent_north': {
        'name': '租赁 - 北部区域',
        'listingType': 'rent',
        'isCommercial': 'false',
        'mrtStations': ['NS14', 'NS15', 'NS16', 'NS17'],
        '_freetextDisplay': 'NS14+Khatib+MRT%2CNS15+Yio+Chu+Kang+MRT%2CNS16+Ang+Mo+Kio+MRT%2CNS17+Bishan+MRT',
        'propertyTypeGroup': 'N',
        'propertyTypeCode': ['CONDO', 'APT', 'WALK', 'CLUS', 'EXCON'],
        'minRent': '2500',
        'maxRent': '6000',
        'bedrooms': ['3', '4', '5']
    }
}

# 默认配置（向后兼容）
TARGET_PARAMS = SEARCH_CONFIGS['sale_bishan_toa_payoh']

# 爬虫行为配置
SCRAPER_CONFIG = {
    'headless': True,           # 是否无头模式运行
    'max_retries': 3,          # 最大重试次数
    'page_load_timeout': 15,   # 页面加载超时时间(秒)
    'element_wait_timeout': 10, # 元素等待超时时间(秒)
    'min_delay': 2,            # 最小延迟时间(秒)
    'max_delay': 5,            # 最大延迟时间(秒)
    'scroll_pause': 1,         # 滚动后暂停时间(秒)
}

# 用户代理列表
USER_AGENTS = [
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
]

# CSS选择器配置
SELECTORS = {
    'listing_cards': '.listing-card-root',
    'listing_title': '.listing-title',
    'listing_link': '.listing-card-link',
    'listing_price': '.listing-price',
    'listing_location': '.listing-location',
    'agent_name': '.agent-name',
    'pagination': '.pagination-component a'
}

# Chrome浏览器路径配置
CHROME_PATHS = {
    'macos': "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
    'windows': "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
    'linux': "/usr/bin/google-chrome"
}

# 输出文件配置
OUTPUT_CONFIG = {
    'csv_fields': [
        'title', 'price', 'address', 'bedrooms', 'bathrooms', 'area',
        'property_type', 'built_year', 'floor_level', 'facing', 'facilities',
        'listing_url', 'listing_date', 'agent_name', 'agent_company', 'scraped_at'
    ],
    'timestamp_format': '%Y%m%d_%H%M%S',
    'encoding': 'utf-8'
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file': 'logs/scraper.log'
}

# 地铁站信息映射
MRT_STATIONS = {
    # 原有站点
    'CC13': 'Serangoon',
    'CC14': 'Lorong Chuan',
    'CC15': 'Bishan',
    'CR11': 'Ang Mo Kio',
    'NE12': 'Serangoon',
    'NS16': 'Ang Mo Kio',
    'NS17': 'Bishan',
    'NS18': 'Braddell',
    'NS19': 'Toa Payoh',

    # 新增站点 - Queenstown/Tiong Bahru区域
    'EW17': 'Tiong Bahru',
    'EW18': 'Redhill',
    'EW19': 'Queenstown',

    # Orchard区域
    'NS22': 'Orchard',
    'NS23': 'Somerset',
    'NS24': 'Dhoby Ghaut',

    # CBD区域
    'NS25': 'City Hall',
    'NS26': 'Raffles Place',
    'EW13': 'City Hall',
    'EW14': 'Raffles Place',

    # 东部区域
    'EW7': 'Eunos',
    'EW8': 'Kembangan',
    'EW9': 'Bedok',
    'EW10': 'Tanah Merah',

    # 北部区域
    'NS14': 'Khatib',
    'NS15': 'Yio Chu Kang'
}
