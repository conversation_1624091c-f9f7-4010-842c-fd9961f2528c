#!/usr/bin/env python3
"""
PropertyGuru爬虫配置文件
包含所有可配置的参数和设置
"""

# 目标网站配置
BASE_URL = "https://www.propertyguru.com.sg/property-for-sale"

# 搜索参数配置
TARGET_PARAMS = {
    'listingType': 'sale',
    'isCommercial': 'false',
    'mrtStations': ['CC13', 'CC14', 'CC15', 'CR11', 'NE12', 'NS16', 'NS17', 'NS18', 'NS19'],
    '_freetextDisplay': 'NS17%2FCC15+Bishan+MRT%2CNS18+<PERSON>dell+MRT%2CNS19+Toa+Payoh+MRT%2CNS16%2FCR11+Ang+Mo+Kio+MRT%2CCC14+Lorong+<PERSON><PERSON>+MRT%2CNE12%2FCC13+Serangoon+MRT',
    'propertyTypeGroup': 'N',
    'minPrice': '2000000',
    'maxPrice': '2500000',
    'bedrooms': ['3', '4', '5']
}

# 爬虫行为配置
SCRAPER_CONFIG = {
    'headless': True,           # 是否无头模式运行
    'max_retries': 3,          # 最大重试次数
    'page_load_timeout': 15,   # 页面加载超时时间(秒)
    'element_wait_timeout': 10, # 元素等待超时时间(秒)
    'min_delay': 2,            # 最小延迟时间(秒)
    'max_delay': 5,            # 最大延迟时间(秒)
    'scroll_pause': 1,         # 滚动后暂停时间(秒)
}

# 用户代理列表
USER_AGENTS = [
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
]

# CSS选择器配置
SELECTORS = {
    'listing_cards': '.listing-card-root',
    'listing_title': '.listing-title',
    'listing_link': '.listing-card-link',
    'listing_price': '.listing-price',
    'listing_location': '.listing-location',
    'agent_name': '.agent-name',
    'pagination': '.pagination-component a'
}

# Chrome浏览器路径配置
CHROME_PATHS = {
    'macos': "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
    'windows': "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
    'linux': "/usr/bin/google-chrome"
}

# 输出文件配置
OUTPUT_CONFIG = {
    'csv_fields': [
        'title', 'price', 'address', 'bedrooms', 'bathrooms', 'area',
        'property_type', 'built_year', 'floor_level', 'facing', 'facilities',
        'listing_url', 'listing_date', 'agent_name', 'agent_company', 'scraped_at'
    ],
    'timestamp_format': '%Y%m%d_%H%M%S',
    'encoding': 'utf-8'
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file': 'logs/scraper.log'
}

# 地铁站信息映射
MRT_STATIONS = {
    'CC13': 'Serangoon',
    'CC14': 'Lorong Chuan', 
    'CC15': 'Bishan',
    'CR11': 'Ang Mo Kio',
    'NE12': 'Serangoon',
    'NS16': 'Ang Mo Kio',
    'NS17': 'Bishan',
    'NS18': 'Braddell',
    'NS19': 'Toa Payoh'
}
