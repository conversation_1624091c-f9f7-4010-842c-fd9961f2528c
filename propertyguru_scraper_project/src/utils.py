#!/usr/bin/env python3
"""
PropertyGuru爬虫工具函数
包含数据处理、分析和格式化等辅助功能
"""

import re
import os
import platform
from datetime import datetime
from typing import List, Dict, Optional, Any


def extract_price_number(price_str: str) -> Optional[int]:
    """
    从价格字符串中提取数字
    
    Args:
        price_str: 价格字符串，如 "S$ 2,180,000"
        
    Returns:
        价格数字，如 2180000，如果提取失败返回None
    """
    if not price_str:
        return None
    
    # 移除货币符号和空格
    cleaned = price_str.replace('S$', '').replace('$', '').replace(' ', '')
    
    # 提取数字
    price_match = re.search(r'[\d,]+', cleaned)
    if price_match:
        try:
            return int(price_match.group().replace(',', ''))
        except ValueError:
            return None
    
    return None


def extract_area_number(area_str: str) -> Optional[float]:
    """
    从面积字符串中提取数字
    
    Args:
        area_str: 面积字符串，如 "1,411 sqft"
        
    Returns:
        面积数字，如 1411.0，如果提取失败返回None
    """
    if not area_str:
        return None
    
    # 提取数字
    area_match = re.search(r'(\d+[,\d]*)', area_str)
    if area_match:
        try:
            return float(area_match.group().replace(',', ''))
        except ValueError:
            return None
    
    return None


def extract_bedroom_count(bedroom_str: str) -> Optional[int]:
    """
    从卧室字符串中提取数量
    
    Args:
        bedroom_str: 卧室字符串，如 "3 bedroom" 或 "3 bed"
        
    Returns:
        卧室数量，如果提取失败返回None
    """
    if not bedroom_str:
        return None
    
    # 提取数字
    bed_match = re.search(r'(\d+)', bedroom_str.lower())
    if bed_match:
        try:
            return int(bed_match.group())
        except ValueError:
            return None
    
    return None


def calculate_price_per_sqft(price: int, area: float) -> Optional[float]:
    """
    计算每平方英尺价格
    
    Args:
        price: 总价
        area: 面积(平方英尺)
        
    Returns:
        每平方英尺价格，如果计算失败返回None
    """
    if not price or not area or area <= 0:
        return None
    
    return round(price / area, 2)


def format_currency(amount: int) -> str:
    """
    格式化货币显示
    
    Args:
        amount: 金额
        
    Returns:
        格式化的货币字符串，如 "S$2,180,000"
    """
    if not amount:
        return "N/A"
    
    return f"S${amount:,}"


def clean_text(text: str) -> str:
    """
    清理文本，移除多余空格和特殊字符
    
    Args:
        text: 原始文本
        
    Returns:
        清理后的文本
    """
    if not text:
        return ""
    
    # 移除多余空格
    cleaned = re.sub(r'\s+', ' ', text.strip())
    
    # 移除特殊字符（保留基本标点）
    cleaned = re.sub(r'[^\w\s\-.,()&/]', '', cleaned)
    
    return cleaned


def get_chrome_path() -> str:
    """
    根据操作系统获取Chrome浏览器路径
    
    Returns:
        Chrome浏览器可执行文件路径
    """
    system = platform.system().lower()
    
    if system == 'darwin':  # macOS
        return "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    elif system == 'windows':
        return "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
    elif system == 'linux':
        return "/usr/bin/google-chrome"
    else:
        return "google-chrome"  # 默认路径


def ensure_directory_exists(directory: str) -> None:
    """
    确保目录存在，如果不存在则创建
    
    Args:
        directory: 目录路径
    """
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)


def generate_filename(prefix: str, extension: str, timestamp: Optional[str] = None) -> str:
    """
    生成带时间戳的文件名
    
    Args:
        prefix: 文件名前缀
        extension: 文件扩展名
        timestamp: 时间戳字符串，如果为None则使用当前时间
        
    Returns:
        完整的文件名
    """
    if not timestamp:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    return f"{prefix}_{timestamp}.{extension}"


def analyze_listing_data(data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    分析房源数据，生成统计信息
    
    Args:
        data: 房源数据列表
        
    Returns:
        包含统计信息的字典
    """
    if not data:
        return {}
    
    total_count = len(data)
    
    # 数据完整性统计
    completeness = {
        'title': sum(1 for item in data if item.get('title')),
        'price': sum(1 for item in data if item.get('price')),
        'address': sum(1 for item in data if item.get('address')),
        'area': sum(1 for item in data if item.get('area')),
        'bedrooms': sum(1 for item in data if item.get('bedrooms')),
        'agent_name': sum(1 for item in data if item.get('agent_name')),
        'listing_url': sum(1 for item in data if item.get('listing_url'))
    }
    
    # 价格分析
    prices = []
    for item in data:
        price = extract_price_number(item.get('price', ''))
        if price:
            prices.append(price)
    
    price_stats = {}
    if prices:
        price_stats = {
            'count': len(prices),
            'min': min(prices),
            'max': max(prices),
            'avg': sum(prices) / len(prices),
            'median': sorted(prices)[len(prices) // 2]
        }
    
    # 面积分析
    areas = []
    for item in data:
        area = extract_area_number(item.get('area', ''))
        if area:
            areas.append(area)
    
    area_stats = {}
    if areas:
        area_stats = {
            'count': len(areas),
            'min': min(areas),
            'max': max(areas),
            'avg': sum(areas) / len(areas),
            'median': sorted(areas)[len(areas) // 2]
        }
    
    # 卧室数量分析
    bedroom_counts = {}
    for item in data:
        bedroom_count = extract_bedroom_count(item.get('bedrooms', ''))
        if bedroom_count:
            bedroom_counts[bedroom_count] = bedroom_counts.get(bedroom_count, 0) + 1
    
    return {
        'total_count': total_count,
        'completeness': completeness,
        'completeness_percentage': {
            key: (value / total_count * 100) for key, value in completeness.items()
        },
        'price_stats': price_stats,
        'area_stats': area_stats,
        'bedroom_distribution': bedroom_counts
    }


def format_analysis_report(analysis: Dict[str, Any]) -> str:
    """
    格式化分析报告为可读文本
    
    Args:
        analysis: 分析结果字典
        
    Returns:
        格式化的报告文本
    """
    if not analysis:
        return "无数据可分析"
    
    report = []
    report.append("数据分析报告")
    report.append("=" * 40)
    
    # 基本统计
    report.append(f"总房源数量: {analysis['total_count']}")
    
    # 数据完整性
    report.append("\n数据完整性:")
    for field, percentage in analysis['completeness_percentage'].items():
        report.append(f"- {field}: {percentage:.1f}%")
    
    # 价格统计
    if analysis.get('price_stats'):
        stats = analysis['price_stats']
        report.append("\n价格统计:")
        report.append(f"- 有效价格数量: {stats['count']}")
        report.append(f"- 最低价格: {format_currency(stats['min'])}")
        report.append(f"- 最高价格: {format_currency(stats['max'])}")
        report.append(f"- 平均价格: {format_currency(int(stats['avg']))}")
        report.append(f"- 中位数价格: {format_currency(stats['median'])}")
    
    # 面积统计
    if analysis.get('area_stats'):
        stats = analysis['area_stats']
        report.append("\n面积统计:")
        report.append(f"- 有效面积数量: {stats['count']}")
        report.append(f"- 最小面积: {stats['min']:.0f} sqft")
        report.append(f"- 最大面积: {stats['max']:.0f} sqft")
        report.append(f"- 平均面积: {stats['avg']:.0f} sqft")
        report.append(f"- 中位数面积: {stats['median']:.0f} sqft")
    
    # 卧室分布
    if analysis.get('bedroom_distribution'):
        report.append("\n卧室数量分布:")
        for bedrooms, count in sorted(analysis['bedroom_distribution'].items()):
            report.append(f"- {bedrooms}卧室: {count}套")
    
    return "\n".join(report)
