import csv
from datetime import datetime, timedelta, timezone

INPUT_CSV = '../data/messages_export.csv'
OUTPUT_CSV = '../data/private_messages_last_7_days.csv'

# 当前时间（带本地时区）和7天前
now = datetime.now(timezone.utc)
seven_days_ago = now - timedelta(days=7)

private_rows = []

with open(INPUT_CSV, 'r', encoding='utf-8') as fin:
    reader = csv.DictReader(fin)
    columns = reader.fieldnames
    for row in reader:
        chat_jid = row.get('chat_jid', '')
        timestamp = row.get('timestamp', '')
        # 跳过群聊
        if '@g.us' in chat_jid:
            continue
        # 解析字符串时间戳
        try:
            # 兼容带时区的 ISO 格式
            msg_time = datetime.fromisoformat(timestamp)
            # 转为 UTC
            if msg_time.tzinfo is not None:
                msg_time = msg_time.astimezone(timezone.utc)
        except Exception:
            continue
        if msg_time >= seven_days_ago:
            private_rows.append(row)

# 过滤掉content为空的消息
private_rows = [r for r in private_rows if r.get('content', '').strip() != '']

# 按时间倒序排序（最新在前）
def parse_ts(ts):
    try:
        return datetime.fromisoformat(ts)
    except Exception:
        return datetime.min
private_rows = sorted(private_rows, key=lambda r: parse_ts(r.get('timestamp', '')), reverse=True)

# 写入新CSV
with open(OUTPUT_CSV, 'w', newline='', encoding='utf-8') as fout:
    writer = csv.DictWriter(fout, fieldnames=columns)
    writer.writeheader()
    writer.writerows(private_rows)

print(f'导出完成，共 {len(private_rows)} 条私人消息，文件保存为 {OUTPUT_CSV}') 

# 生成md报告
MD_OUTPUT = '../data/private_messages_last_7_days.md'
with open(MD_OUTPUT, 'w', encoding='utf-8') as md:
    md.write('# 个人消息报告（最近7天）\n\n')
    md.write(f'- 消息总数: {len(private_rows)}\n')
    md.write(f"- 导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
    md.write('## 全部消息\n\n')
    if private_rows:
        for r in private_rows:
            ts = r.get('timestamp', '')
            content = r.get('content', '')
            md.write(f'{ts}  {content}\n')
    else:
        md.write('无消息数据。\n')
print(f'个人消息md报告已生成：{MD_OUTPUT}') 