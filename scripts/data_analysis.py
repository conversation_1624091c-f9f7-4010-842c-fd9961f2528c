import re
from datetime import datetime, timedelta

# 过滤私聊消息，只保留非群聊内容
def filter_private_chats(input_path='whatsapp.txt', output_path='whatsapp_private.txt'):
    with open(input_path, 'r', encoding='utf-8') as fin, open(output_path, 'w', encoding='utf-8') as fout:
        for line in fin:
            # 群聊一般JID含@g.us，或群名常见关键词
            if ('@g.us' not in line and 'Division' not in line and 'Group' not in line and 'Admin' not in line):
                fout.write(line)

# 提取最近7天所有私聊消息
def extract_recent_private_messages(txt_path='whatsapp_private.txt', days=7):
    with open(txt_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    now = datetime.now()
    time_pattern = re.compile(r'\[(\d{4}-\d{2}-\d{2}) (\d{2}:\d{2}:\d{2})\]')
    recent_msgs = []
    for line in lines:
        m = time_pattern.match(line)
        if m:
            dt_str = m.group(1) + ' ' + m.group(2)
            try:
                dt = datetime.strptime(dt_str, '%Y-%m-%d %H:%M:%S')
            except Exception:
                continue
            if now - dt <= timedelta(days=days):
                recent_msgs.append(line.strip())
    print('--- 最近7天所有私聊消息 ---')
    for msg in recent_msgs:
        print(msg)
    print(f'共{len(recent_msgs)}条')

# 提取约好看房信息
def extract_viewing_info(txt_path='whatsapp_private.txt'):
    with open(txt_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    # 关键词和正则
    view_keywords = [
        'viewing', 'can view', 'see unit', '看房', '约看', 'what time', 'confirm viewing', 'available for viewing', 'view this'
    ]
    project_pattern = re.compile(r'(SALE|For Sale|for-sale)?\s*-\s*([\w\s]+)\s+(\d+\s*Beds?)\s*/\s*S?\$?([\d,]+)', re.IGNORECASE)
    link_pattern = re.compile(r'(https?://[^\s]+)')
    price_pattern = re.compile(r'S?\$([\d,]+)')
    time_pattern = re.compile(r'(\d{1,2}(:\d{2})?\s*(AM|PM|am|pm))|((Sun|Mon|Tue|Wed|Thu|Fri|Sat)[a-z]*\s*\d{0,2}(-\d{0,2})?(\s*pm)?)', re.IGNORECASE)
    contact_pattern = re.compile(r'Agent:\s*([^\n]+)')

    records = []

    for i, line in enumerate(lines):
        # 判断是否为约看相关消息
        if any(k in line for k in view_keywords):
            # 向前查找项目信息
            project, unit, price, link, contact = '', '', '', '', ''
            for j in range(i, max(i-8, -1), -1):
                pj = project_pattern.search(lines[j])
                if pj:
                    project = pj.group(2).strip()
                    unit = pj.group(3).strip()
                    price = pj.group(4).replace(',', '')
                lk = link_pattern.search(lines[j])
                if lk:
                    link = lk.group(1)
                ct = contact_pattern.search(lines[j])
                if ct:
                    contact = ct.group(1).strip()
                if project and link:
                    break
            # 提取时间
            tm = time_pattern.search(line)
            time_str = tm.group(0) if tm else ''
            # 记录
            records.append({
                '时间': time_str,
                '项目名称': project,
                '户型': unit,
                '价格': f'S${price}' if price else '',
                '地址 / 栋号': '',
                '联系方式': contact,
                '链接': link
            })

    # 输出为Markdown表格
    print('| 时间 | 项目名称 | 户型 | 价格 | 地址 / 栋号 | 联系方式 | 链接 |')
    print('| --- | --- | --- | --- | --- | --- | --- |')
    for r in records:
        print(f"| {r['时间']} | {r['项目名称']} | {r['户型']} | {r['价格']} | {r['地址 / 栋号']} | {r['联系方式']} | {r['链接']} |")

if __name__ == '__main__':
    filter_private_chats('whatsapp.txt', 'whatsapp_private.txt')
    extract_recent_private_messages('whatsapp_private.txt', days=7)
    # 如需提取约看房信息，取消下行注释
    # extract_viewing_info('whatsapp_private.txt')
