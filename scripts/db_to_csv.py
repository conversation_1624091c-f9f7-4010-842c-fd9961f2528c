import sqlite3
import csv
import os

# 数据库和导出文件路径
DB_PATH = os.path.expanduser('../../whatsapp-mcp/whatsapp-bridge/store/messages.db')

CSV_PATH = '../data/messages_export.csv'

# 连接数据库
conn = sqlite3.connect(DB_PATH)
cursor = conn.cursor()

# 获取所有字段名
cursor.execute('PRAGMA table_info(messages)')
columns = [col[1] for col in cursor.fetchall()]

# 查询所有数据
cursor.execute('SELECT * FROM messages')
rows = cursor.fetchall()

# 写入 CSV
with open(CSV_PATH, 'w', newline='', encoding='utf-8') as f:
    writer = csv.writer(f)
    writer.writerow(columns)
    writer.writerows(rows)

print(f'导出完成，共 {len(rows)} 条消息，文件保存为 {CSV_PATH}')

# 导出 chats 表的 jid 和 name 字段
CHATS_CSV_PATH = '../data/chats_export.csv'
cursor.execute('SELECT jid, name FROM chats')
chat_rows = cursor.fetchall()

with open(CHATS_CSV_PATH, 'w', newline='', encoding='utf-8') as f:
    writer = csv.writer(f)
    writer.writerow(['jid', 'name'])
    writer.writerows(chat_rows)

print(f'导出完成，共 {len(chat_rows)} 条聊天，文件保存为 {CHATS_CSV_PATH}')

# 关闭连接
conn.close() 