import csv
import os
import re
import datetime

INPUT_CSV = '../data/messages_export.csv'
CSV_OUTPUT_DIR = '../data/groups/csv/'
MD_OUTPUT_DIR = '../data/groups/md/'
USEFUL_GROUPS_CSV = '../data/useful_groups.csv'

# 读取 useful_groups.csv，构建 jid->name 映射
useful_groups = {}
with open(USEFUL_GROUPS_CSV, 'r', encoding='utf-8') as f:
    reader = csv.DictReader(f)
    for row in reader:
        jid = row.get('jid', '')
        name = row.get('name', '')
        if jid:
            useful_groups[jid] = name

# 确保输出目录存在
os.makedirs(CSV_OUTPUT_DIR, exist_ok=True)
os.makedirs(MD_OUTPUT_DIR, exist_ok=True)

group_messages = {}

# 读取并分组，只保留 useful_groups 里的群聊
with open(INPUT_CSV, 'r', encoding='utf-8') as fin:
    reader = csv.DictReader(fin)
    columns = reader.fieldnames
    for row in reader:
        chat_jid = row.get('chat_jid', '')
        if chat_jid not in useful_groups:
            continue
        if chat_jid not in group_messages:
            group_messages[chat_jid] = []
        # 增加 name 字段
        row['name'] = useful_groups[chat_jid]
        group_messages[chat_jid].append(row)

def safe_filename(name):
    return re.sub(r'[^\w\-]', '_', name)

# 导出每个群聊，字段加上 name
export_columns = columns + ['name'] if 'name' not in columns else columns
for chat_jid, rows in group_messages.items():
    group_name = useful_groups[chat_jid]
    # 过滤掉content为空的消息
    rows_nonempty = [r for r in rows if r.get('content', '').strip() != '']
    # 按时间倒序排序（最新在前）
    def parse_ts(ts):
        try:
            # 支持带时区的格式
            return datetime.datetime.fromisoformat(ts)
        except Exception:
            return datetime.datetime.min
    rows_sorted = sorted(rows_nonempty, key=lambda r: parse_ts(r.get('timestamp', '')), reverse=True)

    filename = f'group_{safe_filename(group_name)}.csv'
    csv_out_path = os.path.join(CSV_OUTPUT_DIR, filename)
    with open(csv_out_path, 'w', newline='', encoding='utf-8') as fout:
        writer = csv.DictWriter(fout, fieldnames=export_columns)
        writer.writeheader()
        writer.writerows(rows_sorted)
    print(f'导出群聊 {chat_jid}（{group_name}），共 {len(rows_sorted)} 条消息，文件：{csv_out_path}')

    # 生成md报告，展示所有消息
    md_filename = f'group_{safe_filename(group_name)}.md'
    md_out_path = os.path.join(MD_OUTPUT_DIR, md_filename)
    with open(md_out_path, 'w', encoding='utf-8') as md:
        md.write(f'# 群聊报告：{group_name}\n\n')
        md.write(f'- 群聊JID: `{chat_jid}`\n')
        md.write(f'- 消息总数: {len(rows_sorted)}\n')
        md.write(f"- 导出时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        md.write('## 全部消息\n\n')
        if rows_sorted:
            for r in rows_sorted:
                ts = r.get('timestamp', '')
                content = r.get('content', '')
                md.write(f'{ts}  {content}\n')
        else:
            md.write('无消息数据。\n') 